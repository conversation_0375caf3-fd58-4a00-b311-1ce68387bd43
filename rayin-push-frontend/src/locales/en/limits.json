{"title": "Request Limits", "rateLimits": "Rate Limits", "ipLimits": "IP Limits", "createRule": "Create Rule", "editRule": "Edit Rule", "deleteRule": "Delete Rule", "ruleName": "Rule Name", "ruleDescription": "Rule Description", "timeWindow": "Time Window", "requestLimit": "Request Limit", "ruleStatus": "Rule Status", "ruleType": "Rule Type", "globalLimit": "Global Limit", "perIpLimit": "Per IP Limit", "perUserLimit": "Per User Limit", "seconds": "Seconds", "minutes": "Minutes", "hours": "Hours", "days": "Days", "requestsPerTimeWindow": "Requests per Time Window", "ipAddress": "IP Address", "ipRange": "IP Range", "singleIp": "Single IP", "ipWhitelist": "IP Whitelist", "ipBlacklist": "IP Blacklist", "addIpRule": "Add IP Rule", "removeIpRule": "Remove IP Rule", "currentLimits": "Current Limits", "activeLimits": "Active Limits", "limitStatistics": "Limit Statistics", "triggeredCount": "Triggered Count", "lastTriggered": "Last Triggered", "affectedRequests": "Affected Requests", "exemptIps": "Exempt IPs", "exemptUsers": "Exempt Users", "enableRule": "Enable Rule", "disableRule": "Disable Rule", "ruleHistory": "Rule History", "limitExceeded": "Limit Exceeded", "rateLimitReset": "Rate Limit Reset Time"}