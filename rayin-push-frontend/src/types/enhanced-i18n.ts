// Enhanced i18n types with pluralization and formatting support

import type { 
  TranslationNamespace, 
  TranslationTypeMap 
} from './i18n'

// Pluralization rules
export interface PluralRule {
  zero?: string
  one?: string
  two?: string
  few?: string
  many?: string
  other: string
}

// Formatting options
export interface FormatOptions {
  // Number formatting
  number?: {
    style?: 'decimal' | 'currency' | 'percent'
    currency?: string
    minimumFractionDigits?: number
    maximumFractionDigits?: number
  }
  
  // Date formatting
  date?: {
    style?: 'short' | 'medium' | 'long' | 'full'
    dateStyle?: 'short' | 'medium' | 'long' | 'full'
    timeStyle?: 'short' | 'medium' | 'long' | 'full'
  }
  
  // Relative time formatting
  relativeTime?: {
    unit?: 'second' | 'minute' | 'hour' | 'day' | 'week' | 'month' | 'year'
    numeric?: 'always' | 'auto'
  }
}

// Enhanced translation options
export interface EnhancedTranslationOptions extends FormatOptions {
  count?: number
  context?: string
  defaultValue?: string
  interpolation?: Record<string, any>
  [key: string]: any
}

// Translation key with pluralization support
export type TranslationKey<T extends TranslationNamespace> = 
  | keyof TranslationTypeMap[T]
  | `${string & keyof TranslationTypeMap[T]}_${string}`

// Enhanced translation function type
export type TranslationFunction<T extends TranslationNamespace> = (
  key: TranslationKey<T>,
  options?: string | EnhancedTranslationOptions
) => string

// Translation utilities
export interface TranslationUtils {
  formatNumber: (value: number, options?: FormatOptions['number']) => string
  formatDate: (date: Date, options?: FormatOptions['date']) => string
  formatRelativeTime: (date: Date, options?: FormatOptions['relativeTime']) => string
  formatCurrency: (amount: number, currency: string) => string
  formatPercent: (value: number) => string
}

// Context-aware translation keys
export interface ContextualTranslations {
  // Form states
  form: {
    required: string
    invalid: string
    valid: string
    loading: string
    submitting: string
  }
  
  // Data states
  data: {
    loading: string
    empty: string
    error: string
    retry: string
  }
  
  // Actions with pluralization
  actions: {
    delete: string
    delete_one: string
    delete_other: string
    selected: string
    selected_one: string
    selected_other: string
  }
  
  // Time-related translations
  time: {
    now: string
    minute_ago: string
    minutes_ago: string // {{count}} minutes ago
    hour_ago: string
    hours_ago: string // {{count}} hours ago
    day_ago: string
    days_ago: string // {{count}} days ago
  }
}

// Rich text translation support
export interface RichTextTranslation {
  key: string
  components?: Record<string, React.ComponentType<any>>
  values?: Record<string, any>
}

export type RichTranslationFunction<T extends TranslationNamespace> = (
  key: TranslationKey<T>,
  richOptions?: RichTextTranslation
) => React.ReactNode

// Translation metadata
export interface TranslationMetadata {
  namespace: TranslationNamespace
  version: string
  lastUpdated: string
  completeness: number // percentage
  pluralRules?: string[] // supported plural forms
}

// Translation validation
export interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
  missingKeys: string[]
  unusedKeys: string[]
}

// Export enhanced types
export type {
  TranslationNamespace,
  TranslationTypeMap
}