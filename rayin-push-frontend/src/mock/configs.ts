import type { InterfaceConfig } from '@/types/data'

export const mockInterfaceConfigs: InterfaceConfig[] = [
  {
    id: '1',
    name: '用户注册通知阿斯顿发123阿斯顿发斯地方大法师地方',
    token: '550e8400-e29b-41d4-a716-446655440001',
    description: '用户注册成功后12312312341234123阿斯顿发斯的法师的法师大法师地方发送通知',
    method: 'POST',
    status: 'enabled',
    channels: ['1', '2'],
    parsingRules: {
      type: 'post-json',
      variableMapping: {
        'user.name': 'userName',
        'user.email': 'userEmail', 
        'timestamp': 'registerTime',
        'user.type': 'userType'
      }
    },
    createdTime: '2024-01-15 10:00:00',
    updatedTime: '2024-12-20 14:30:00'
  },
  {
    id: '2',
    name: '订单状态更新',
    token: '6ba7b810-9dad-11d1-80b4-00c04fd430c8',
    description: '订单状态变更时发送通知',
    method: 'POST',
    status: 'enabled',
    channels: ['1', '3'],
    parsingRules: {
      type: 'post-json',
      variableMapping: {
        'orderId': 'order.id',
        'orderStatus': 'order.status',
        'orderAmount': 'order.amount',
        'customerName': 'customer.name'
      }
    },
    createdTime: '2024-02-01 09:30:00',
    updatedTime: '2024-12-18 16:20:00'
  },
  {
    id: '3',
    name: '系统异常告警',
    token: '6ba7b811-9dad-11d1-80b4-00c04fd430c8',
    description: '系统出现异常时发送告警通知',
    method: 'GET',
    status: 'enabled',
    channels: ['2', '4'],
    parsingRules: {
      type: 'get',
      variableMapping: {
        'alert_level': 'level',
        'alert_message': 'message',
        'service_name': 'service',
        'occurred_at': 'timestamp'
      }
    },
    createdTime: '2024-01-20 11:15:00',
    updatedTime: '2024-12-15 13:45:00'
  },
  {
    id: '4',
    name: '支付成功通知',
    token: '6ba7b812-9dad-11d1-80b4-00c04fd430c8',
    description: '用户支付成功后发送确认通知',
    method: 'POST',
    status: 'disabled',
    channels: ['1'],
    parsingRules: {
      type: 'post-form',
      variableMapping: {
        'payment_id': 'paymentId',
        'amount': 'paymentAmount',
        'currency': 'currency',
        'payer_name': 'payerName'
      }
    },
    createdTime: '2024-03-05 15:20:00',
    updatedTime: '2024-11-30 10:10:00'
  },
  {
    id: '5',
    name: '日志分析结果',
    token: '6ba7b813-9dad-11d1-80b4-00c04fd430c8',
    description: '每日日志分析结果推送',
    method: 'POST',
    status: 'enabled',
    channels: ['2'],
    parsingRules: {
      type: 'post-plain',
      variableMapping: {},
      regexPatterns: {
        'date': 'Date: (\\d{4}-\\d{2}-\\d{2})',
        'errorCount': 'Errors: (\\d+)',
        'warningCount': 'Warnings: (\\d+)',
        'summary': 'Summary: (.+)'
      }
    },
    createdTime: '2024-04-10 08:45:00',
    updatedTime: '2024-12-10 09:30:00'
  },
  {
    id: '6',
    name: '库存预警通知',
    token: '6ba7b814-9dad-11d1-80b4-00c04fd430c8',
    description: '商品库存低于预警值时发送通知',
    method: 'POST',
    status: 'enabled',
    channels: ['1', '2'],
    parsingRules: {
      type: 'post-json',
      variableMapping: {
        'product.id': 'productId',
        'product.name': 'productName',
        'current_stock': 'currentStock',
        'threshold': 'warningThreshold'
      }
    },
    createdTime: '2024-05-15 14:20:00',
    updatedTime: '2024-12-05 16:45:00'
  },
  {
    id: '7',
    name: '用户登录异常',
    token: '6ba7b815-9dad-11d1-80b4-00c04fd430c8',
    description: '检测到异常登录行为时发送安全警告',
    method: 'POST',
    status: 'enabled',
    channels: ['2', '3'],
    parsingRules: {
      type: 'post-json',
      variableMapping: {
        'user.id': 'userId',
        'login_ip': 'loginIp',
        'location': 'loginLocation',
        'device': 'deviceInfo'
      }
    },
    createdTime: '2024-06-20 09:15:00',
    updatedTime: '2024-11-28 11:30:00'
  },
  {
    id: '8',
    name: '数据备份完成',
    token: '6ba7b816-9dad-11d1-80b4-00c04fd430c8',
    description: '每日数据备份任务完成后发送状态通知',
    method: 'GET',
    status: 'enabled',
    channels: ['3'],
    parsingRules: {
      type: 'get',
      variableMapping: {
        'backup_date': 'date',
        'backup_size': 'size',
        'backup_status': 'status',
        'backup_duration': 'duration'
      }
    },
    createdTime: '2024-07-10 22:00:00',
    updatedTime: '2024-12-01 08:15:00'
  },
  {
    id: '9',
    name: '活动推广通知',
    token: '6ba7b817-9dad-11d1-80b4-00c04fd430c8',
    description: '新活动上线时向用户推送活动信息',
    method: 'POST',
    status: 'disabled',
    channels: ['1', '4'],
    parsingRules: {
      type: 'post-form',
      variableMapping: {
        'activity_name': 'activityName',
        'start_time': 'startTime',
        'end_time': 'endTime',
        'discount': 'discountRate'
      }
    },
    createdTime: '2024-08-25 16:30:00',
    updatedTime: '2024-10-15 12:20:00'
  },
  {
    id: '10',
    name: '服务器监控报告',
    token: '6ba7b818-9dad-11d1-80b4-00c04fd430c8',
    description: '服务器性能监控数据定时推送',
    method: 'POST',
    status: 'enabled',
    channels: ['2'],
    parsingRules: {
      type: 'post-plain',
      variableMapping: {},
      regexPatterns: {
        'cpu_usage': 'CPU: ([0-9.]+)%',
        'memory_usage': 'Memory: ([0-9.]+)%',
        'disk_usage': 'Disk: ([0-9.]+)%',
        'uptime': 'Uptime: (.+)'
      }
    },
    createdTime: '2024-09-12 13:45:00',
    updatedTime: '2024-12-08 10:25:00'
  },
  {
    id: '11',
    name: '客户反馈收集',
    token: '6ba7b819-9dad-11d1-80b4-00c04fd430c8',
    description: '客户提交反馈时自动转发到相关部门',
    method: 'POST',
    status: 'enabled',
    channels: ['1', '3', '4'],
    parsingRules: {
      type: 'post-json',
      variableMapping: {
        'customer.name': 'customerName',
        'customer.email': 'customerEmail',
        'feedback.type': 'feedbackType',
        'feedback.content': 'feedbackContent'
      }
    },
    createdTime: '2024-10-30 11:10:00',
    updatedTime: '2024-12-12 15:40:00'
  }
]