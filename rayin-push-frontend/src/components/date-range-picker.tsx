'use client'

import React, { useState } from 'react'
import { Calendar, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { cn } from '@/lib/utils'
import type { DateRange, DateRangePickerProps } from '@/types/filters'

// 简单的日期工具函数
const formatDate = (date: Date | null): string => {
  if (!date) return ''
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

const parseDate = (dateString: string): Date | null => {
  if (!dateString) return null
  const date = new Date(dateString)
  return isNaN(date.getTime()) ? null : date
}

const isValidDateRange = (range: DateRange): boolean => {
  if (!range.start || !range.end) return true
  return range.start <= range.end
}

// 预设时间范围
const getDefaultPresets = (): { label: string; range: DateRange }[] => {
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)
  
  const last7Days = new Date(today)
  last7Days.setDate(last7Days.getDate() - 7)
  
  const last30Days = new Date(today)
  last30Days.setDate(last30Days.getDate() - 30)
  
  const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1)
  
  const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1)
  const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0)

  return [
    {
      label: '今天',
      range: { start: today, end: today }
    },
    {
      label: '昨天',
      range: { start: yesterday, end: yesterday }
    },
    {
      label: '最近 7 天',
      range: { start: last7Days, end: today }
    },
    {
      label: '最近 30 天',
      range: { start: last30Days, end: today }
    },
    {
      label: '本月',
      range: { start: thisMonth, end: today }
    },
    {
      label: '上月',
      range: { start: lastMonth, end: lastMonthEnd }
    }
  ]
}

export function DateRangePicker({
  value,
  onChange,
  disabled = false,
  className,
  placeholder = '选择日期范围',
  presets,
  maxDate,
  minDate
}: DateRangePickerProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [startDateInput, setStartDateInput] = useState(
    value.start ? value.start.toISOString().split('T')[0] : ''
  )
  const [endDateInput, setEndDateInput] = useState(
    value.end ? value.end.toISOString().split('T')[0] : ''
  )

  const defaultPresets = presets || getDefaultPresets()

  const handleStartDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const dateString = e.target.value
    setStartDateInput(dateString)
    
    const startDate = parseDate(dateString)
    const newRange = { start: startDate, end: value.end }
    
    if (isValidDateRange(newRange)) {
      onChange(newRange)
    }
  }

  const handleEndDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const dateString = e.target.value
    setEndDateInput(dateString)
    
    const endDate = parseDate(dateString)
    const newRange = { start: value.start, end: endDate }
    
    if (isValidDateRange(newRange)) {
      onChange(newRange)
    }
  }

  const handlePresetClick = (preset: { label: string; range: DateRange }) => {
    onChange(preset.range)
    setStartDateInput(preset.range.start ? preset.range.start.toISOString().split('T')[0] : '')
    setEndDateInput(preset.range.end ? preset.range.end.toISOString().split('T')[0] : '')
    setIsOpen(false)
  }

  const handleClear = () => {
    onChange({ start: null, end: null })
    setStartDateInput('')
    setEndDateInput('')
  }

  const displayValue = () => {
    if (!value.start && !value.end) return placeholder
    if (value.start && value.end) {
      return `${formatDate(value.start)} - ${formatDate(value.end)}`
    }
    if (value.start) return `${formatDate(value.start)} - `
    if (value.end) return ` - ${formatDate(value.end)}`
    return placeholder
  }

  const hasValue = value.start || value.end

  // 计算日期输入的限制
  const getDateInputProps = (type: 'start' | 'end') => {
    const props: React.InputHTMLAttributes<HTMLInputElement> = {
      type: 'date',
      disabled
    }
    
    if (minDate) {
      props.min = minDate.toISOString().split('T')[0]
    }
    
    if (maxDate) {
      props.max = maxDate.toISOString().split('T')[0]
    }
    
    if (type === 'start' && value.end) {
      props.max = value.end.toISOString().split('T')[0]
    }
    
    if (type === 'end' && value.start) {
      props.min = value.start.toISOString().split('T')[0]
    }
    
    return props
  }

  return (
    <div className={cn('relative', className)}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            disabled={disabled}
            className={cn(
              'w-full justify-start text-left font-normal',
              !hasValue && 'text-muted-foreground'
            )}
          >
            <Calendar className="mr-2 h-4 w-4" />
            <span className="flex-1 truncate">{displayValue()}</span>
            {hasValue && (
              <X
                className="ml-2 h-4 w-4 hover:text-destructive cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation()
                  handleClear()
                }}
              />
            )}
          </Button>
        </PopoverTrigger>
        
        <PopoverContent className="w-auto p-0" align="start">
          <div className="p-4 space-y-4">
            {/* 快速选择预设 */}
            <div>
              <h4 className="font-medium text-sm mb-2">快速选择</h4>
              <div className="grid grid-cols-2 gap-2">
                {defaultPresets.map((preset) => (
                  <Button
                    key={preset.label}
                    variant="outline"
                    size="sm"
                    onClick={() => handlePresetClick(preset)}
                    className="justify-start"
                  >
                    {preset.label}
                  </Button>
                ))}
              </div>
            </div>

            {/* 自定义日期范围 */}
            <div>
              <h4 className="font-medium text-sm mb-2">自定义范围</h4>
              <div className="space-y-2">
                <div>
                  <label className="text-xs text-muted-foreground">开始日期</label>
                  <Input
                    {...getDateInputProps('start')}
                    value={startDateInput}
                    onChange={handleStartDateChange}
                    className="w-full"
                  />
                </div>
                <div>
                  <label className="text-xs text-muted-foreground">结束日期</label>
                  <Input
                    {...getDateInputProps('end')}
                    value={endDateInput}
                    onChange={handleEndDateChange}
                    className="w-full"
                  />
                </div>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex justify-between pt-2">
              <Button variant="outline" size="sm" onClick={handleClear}>
                清除
              </Button>
              <Button size="sm" onClick={() => setIsOpen(false)}>
                确定
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}