'use client'

import { ReactNode, memo } from 'react'
import { TopNavBar } from '@/components/header'
import type { BreadcrumbItem } from '@/types/breadcrumb'

interface AppLayoutProps {
  children: ReactNode
  title?: string
  breadcrumbItems?: BreadcrumbItem[]
  showSearch?: boolean
}

function AppLayoutComponent({
  children,
  title,
  breadcrumbItems,
  showSearch = true
}: AppLayoutProps) {
  return (
    <>
      {/* 顶部导航栏 */}
      <TopNavBar
        title={title}
        breadcrumbItems={breadcrumbItems}
        showSearch={showSearch}
      />

      {/* 主内容 */}
      <main className="flex-1 overflow-y-auto">
        <div className="h-full">
          {children}
        </div>
      </main>
    </>
  )
}

export const AppLayout = memo(AppLayoutComponent)