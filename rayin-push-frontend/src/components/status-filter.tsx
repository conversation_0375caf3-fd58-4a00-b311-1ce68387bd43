'use client'

import React, { useState } from 'react'
import { Check, ChevronDown, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import type { StatusFilterProps, FilterOption } from '@/types/filters'

export function StatusFilter<T = unknown>({
  options,
  value,
  onChange,
  placeholder = '选择状态',
  multiple = false,
  disabled = false,
  className,
  showClearButton = true,
  showSelectedItems = true,
  renderOption
}: StatusFilterProps<T>) {
  const [isOpen, setIsOpen] = useState(false)

  // 获取选中的选项
  const getSelectedOptions = (): FilterOption<T>[] => {
    if (!value) return []
    
    if (multiple) {
      const values = Array.isArray(value) ? value : [value]
      return options.filter(option => values.includes(option.value))
    } else {
      return options.filter(option => option.value === value)
    }
  }

  const selectedOptions = getSelectedOptions()

  // 处理选项点击
  const handleOptionClick = (option: FilterOption<T>) => {
    if (option.disabled) return

    if (multiple) {
      const currentValues = Array.isArray(value) ? value : (value ? [value] : [])
      const isSelected = currentValues.includes(option.value)
      
      if (isSelected) {
        const newValues = currentValues.filter(v => v !== option.value)
        onChange(newValues.length > 0 ? newValues : undefined)
      } else {
        onChange([...currentValues, option.value])
      }
    } else {
      if (value === option.value) {
        onChange(undefined)
      } else {
        onChange(option.value)
      }
      setIsOpen(false)
    }
  }

  // 清除选择
  const handleClear = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    onChange(undefined)
  }

  // 移除单个选项（多选模式）
  const handleRemoveOption = (optionValue: T, e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (!multiple) return

    const currentValues = Array.isArray(value) ? value : (value ? [value] : [])
    const newValues = currentValues.filter(v => v !== optionValue)
    onChange(newValues.length > 0 ? newValues : undefined)
  }

  // 检查选项是否被选中
  const isOptionSelected = (option: FilterOption<T>): boolean => {
    if (!value) return false
    
    if (multiple) {
      const values = Array.isArray(value) ? value : [value]
      return values.includes(option.value)
    } else {
      return value === option.value
    }
  }

  // 显示文本
  const getDisplayText = (): React.ReactNode => {
    if (selectedOptions.length === 0) {
      return <span className="text-muted-foreground">{placeholder}</span>
    }

    if (multiple) {
      if (selectedOptions.length === 1) {
        return (
          <span 
            className="truncate max-w-[200px]" 
            title={selectedOptions[0].label}
          >
            {selectedOptions[0].label}
          </span>
        )
      }
      
      return (
        <div className="flex items-start gap-1 flex-wrap">
          {selectedOptions.map((option, index) => (
            <Badge
              key={index}
              variant="secondary"
              className="text-xs px-2 py-1 whitespace-nowrap"
              title={option.label}
            >
              {option.label}
            </Badge>
          ))}
        </div>
      )
    } else {
      return (
        <span 
          className="truncate max-w-[200px]" 
          title={selectedOptions[0]?.label}
        >
          {selectedOptions[0]?.label}
        </span>
      )
    }
  }

  // 获取选中数量的统计
  const getTotalCount = (): number => {
    return selectedOptions.reduce((sum, option) => sum + (option.count || 0), 0)
  }

  const hasValue = selectedOptions.length > 0

  return (
    <div className={cn('relative', className)}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            disabled={disabled}
            className={cn(
              'w-full justify-between text-left font-normal min-h-[40px] h-auto py-2 hover:bg-transparent',
              !hasValue && 'text-muted-foreground',
              className
            )}
          >
            <div className="flex items-start flex-1 min-w-0">
              {getDisplayText()}
            </div>
            <div className="flex items-center gap-1 ml-2">
              <ChevronDown className="h-4 w-4 opacity-50" />
            </div>
          </Button>
        </PopoverTrigger>
        
        <PopoverContent className="w-full p-0" align="start">
          <div className="max-h-60 overflow-y-auto overscroll-contain" onWheel={(e) => e.stopPropagation()}>
            {options.length === 0 ? (
              <div className="p-4 text-center text-sm text-muted-foreground">
                暂无选项
              </div>
            ) : (
              <div className="p-1">
                {options.map((option, index) => {
                  const isSelected = isOptionSelected(option)
                  
                  return (
                    <div
                      key={`${option.value}-${index}`}
                      className={cn(
                        'flex items-start justify-between px-3 py-3 text-sm cursor-pointer hover:bg-accent hover:text-accent-foreground rounded-sm',
                        option.disabled && 'opacity-50 cursor-not-allowed'
                      )}
                      onClick={() => handleOptionClick(option)}
                    >
                      <div className="flex items-start gap-2 flex-1 min-w-0">
                        {multiple && (
                          <div className={cn(
                            'w-4 h-4 border border-primary rounded flex items-center justify-center mt-0.5',
                            isSelected && 'bg-primary text-primary-foreground'
                          )}>
                            {isSelected && <Check className="h-3 w-3" />}
                          </div>
                        )}
                        
                        <div className="flex-1 min-w-0">
                          {renderOption ? (
                            renderOption(option, isSelected)
                          ) : (
                            <>
                              <span className="truncate">{option.label}</span>
                              {option.count !== undefined && (
                                <Badge variant="outline" className="h-5 text-xs ml-auto">
                                  {option.count}
                                </Badge>
                              )}
                            </>
                          )}
                        </div>
                      </div>
                      
                      {!multiple && isSelected && (
                        <Check className="h-4 w-4" />
                      )}
                    </div>
                  )
                })}
              </div>
            )}
          </div>
          
          {/* 多选模式的操作区域 */}
          {multiple && hasValue && (
            <div className="border-t p-2 bg-muted/30">
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <span>已选择 {selectedOptions.length} 项</span>
                {getTotalCount() > 0 && (
                  <span>共 {getTotalCount()} 条</span>
                )}
              </div>
            </div>
          )}
        </PopoverContent>
      </Popover>
      
      {/* 多选模式下的选中项展示 */}
      {multiple && hasValue && showSelectedItems && (
        <div className="mt-2 p-2 border rounded-md bg-background min-h-[40px] flex flex-wrap gap-1 items-center">
          {selectedOptions.map((option, index) => (
            <Badge
              key={`selected-${option.value}-${index}`}
              variant="secondary"
              className="text-xs px-2 py-1 max-w-[200px] flex items-center"
              title={option.label}
            >
              <span className="truncate">{option.label}</span>
            </Badge>
          ))}
          {selectedOptions.length === 0 && (
            <span className="text-muted-foreground text-sm">暂无选择</span>
          )}
        </div>
      )}
    </div>
  )
}