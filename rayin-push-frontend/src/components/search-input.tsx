'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { Search, X } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import type { SearchBoxProps } from '@/types/filters'

// 防抖 Hook
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

export function SearchBox({
  value,
  onChange,
  placeholder = '搜索...',
  disabled = false,
  className,
  debounceMs = 300,
  onSearch,
  onClear,
  showClearButton = true
}: SearchBoxProps) {
  const [inputValue, setInputValue] = useState(value)
  const debouncedValue = useDebounce(inputValue, debounceMs)

  // 当外部 value 改变时同步内部状态
  useEffect(() => {
    setInputValue(value)
  }, [value])

  // 当防抖值改变时触发 onChange
  useEffect(() => {
    if (debouncedValue !== value) {
      onChange(debouncedValue)
      onSearch?.(debouncedValue)
    }
  }, [debouncedValue, value, onChange, onSearch])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value)
  }

  const handleClear = useCallback(() => {
    setInputValue('')
    onChange('')
    onClear?.()
  }, [onChange, onClear])

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      onSearch?.(inputValue)
    } else if (e.key === 'Escape') {
      handleClear()
    }
  }

  return (
    <div className={cn('relative', className)}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          disabled={disabled}
          className={cn(
            'pl-10',
            showClearButton && inputValue && 'pr-10'
          )}
        />
        {showClearButton && inputValue && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={handleClear}
            disabled={disabled}
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7 p-0 hover:bg-muted"
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>
    </div>
  )
}