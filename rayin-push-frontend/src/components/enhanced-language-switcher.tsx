'use client'

import { useCallback, useMemo } from 'react'
import { useLocale } from '@/hooks/use-locale'
import { useTypedTranslation } from '@/hooks/use-typed-translation'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Languages, Check, Globe } from 'lucide-react'
import { cn } from '@/lib/utils'

interface LanguageOption {
  code: string
  name: string
  nativeName: string
  flag: string
  direction: 'ltr' | 'rtl'
}

// Extensible language configuration
const SUPPORTED_LANGUAGES: LanguageOption[] = [
  {
    code: 'zh',
    name: 'Chinese',
    nativeName: '中文',
    flag: '🇨🇳',
    direction: 'ltr'
  },
  {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
    direction: 'ltr'
  }
  // Easy to extend with more languages
  // {
  //   code: 'ja',
  //   name: 'Japanese',
  //   nativeName: '日本語',
  //   flag: '🇯🇵',
  //   direction: 'ltr'
  // }
]

interface LanguageSwitchProps {
  compact?: boolean
  className?: string
  variant?: 'icon' | 'text' | 'full'
  showFlag?: boolean
  showNativeName?: boolean
}

export function LanguageSwitch({ 
  compact = false, 
  className,
  variant = 'icon',
  showFlag = true,
  showNativeName = true
}: LanguageSwitchProps) {
  const { switchLocale, locale } = useLocale()
  const { t } = useTypedTranslation('common')
  
  // Get current language info
  const currentLanguage = useMemo(() => 
    SUPPORTED_LANGUAGES.find(lang => lang.code === locale) || SUPPORTED_LANGUAGES[0],
    [locale]
  )

  // Handle language change with loading state
  const handleLanguageChange = useCallback(async (newLocale: string) => {
    try {
      // Could add loading state here
      switchLocale(newLocale)
      
      // Store user preference
      if (typeof window !== 'undefined') {
        localStorage.setItem('preferred-language', newLocale)
      }
    } catch (error) {
      console.error('Failed to switch language:', error)
    }
  }, [switchLocale])

  // Render button content based on variant
  const renderButtonContent = () => {
    switch (variant) {
      case 'text':
        return (
          <div className="flex items-center gap-2">
            {showFlag && <span>{currentLanguage.flag}</span>}
            <span className="text-sm">{currentLanguage.nativeName}</span>
          </div>
        )
      case 'full':
        return (
          <div className="flex items-center gap-2">
            <Globe className="h-4 w-4" />
            {showFlag && <span>{currentLanguage.flag}</span>}
            <span className="text-sm">{currentLanguage.nativeName}</span>
          </div>
        )
      default: // icon
        return <Languages className="h-4 w-4" />
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size={compact ? "sm" : "default"}
          className={cn(
            variant === 'icon' && "h-8 w-8 p-0",
            className
          )}
          title={t('language')}
        >
          {renderButtonContent()}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="min-w-[180px]">
        {SUPPORTED_LANGUAGES.map((language) => (
          <DropdownMenuItem 
            key={language.code}
            onClick={() => handleLanguageChange(language.code)} 
            className="flex items-center justify-between cursor-pointer"
            disabled={language.code === locale}
          >
            <div className="flex items-center gap-3">
              {showFlag && (
                <span className="text-base" role="img" aria-label={language.name}>
                  {language.flag}
                </span>
              )}
              <div>
                <div className="font-medium">{language.nativeName}</div>
                {showNativeName && language.nativeName !== language.name && (
                  <div className="text-xs text-muted-foreground">
                    {language.name}
                  </div>
                )}
              </div>
            </div>
            {locale === language.code && (
              <Check className="h-4 w-4 text-primary" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

// Export language configuration for other components
export { SUPPORTED_LANGUAGES, type LanguageOption }