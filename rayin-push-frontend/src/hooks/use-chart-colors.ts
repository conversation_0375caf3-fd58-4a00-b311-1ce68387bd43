'use client'

import { useState, useEffect } from 'react'

interface ChartColors {
  foreground: string
  mutedForeground: string
  border: string
  success: string
  error: string
  warning: string
  primary: string
}

export function useChartColors(): ChartColors {
  const [colors, setColors] = useState<ChartColors>({
    foreground: 'oklch(0.145 0 0)',
    mutedForeground: 'oklch(0.5 0 0)',
    border: 'oklch(0.9 0 0)',
    success: '#10b981',
    error: '#ef4444',
    warning: '#f59e0b',
    primary: 'oklch(0.5 0.2 250)'
  })

  useEffect(() => {
    const updateColors = () => {
      const style = getComputedStyle(document.documentElement)
      const isDark = document.documentElement.classList.contains('dark')
      
      // 获取CSS变量值，如果没有则使用默认值
      const foregroundValue = style.getPropertyValue('--foreground').trim()
      const mutedForegroundValue = style.getPropertyValue('--muted-foreground').trim()
      const borderValue = style.getPropertyValue('--border').trim()
      const primaryValue = style.getPropertyValue('--primary').trim()
      
      setColors({
        foreground: foregroundValue || (isDark ? 'oklch(0.92 0 0)' : 'oklch(0.145 0 0)'),
        mutedForeground: mutedForegroundValue || (isDark ? 'oklch(0.6 0 0)' : 'oklch(0.5 0 0)'),
        border: borderValue || (isDark ? 'oklch(0.3 0 0)' : 'oklch(0.9 0 0)'),
        success: '#10b981',
        error: '#ef4444', 
        warning: '#f59e0b',
        primary: primaryValue || (isDark ? 'oklch(0.7 0.2 250)' : 'oklch(0.5 0.2 250)')
      })
    }

    // 初始化颜色
    updateColors()
    
    // 监听主题变化
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && 
            (mutation.attributeName === 'class' || mutation.attributeName === 'data-theme')) {
          updateColors()
        }
      })
    })
    
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class', 'data-theme']
    })
    
    // 监听存储变化（如果主题通过 localStorage 管理）
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'theme' || e.key === 'dark-mode') {
        updateColors()
      }
    }
    
    window.addEventListener('storage', handleStorageChange)
    
    return () => {
      observer.disconnect()
      window.removeEventListener('storage', handleStorageChange)
    }
  }, [])

  return colors
}