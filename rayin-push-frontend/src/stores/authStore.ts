'use client'

import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { AuthState, User } from '@/types/store'

// 模拟登录API
const mockLogin = async (credentials: { username: string; password: string }): Promise<User> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  // 简单的模拟验证
  if (credentials.username === 'admin' && credentials.password === 'admin') {
    return {
      id: '1',
      username: 'admin',
      email: '<EMAIL>',
      role: 'admin',
      createdTime: '2024-01-01T00:00:00Z',
      lastLoginAt: new Date().toISOString(),
    }
  } else if (credentials.username === 'user' && credentials.password === 'user') {
    return {
      id: '2',
      username: 'user',
      email: '<EMAIL>',
      role: 'user',
      createdTime: '2024-01-01T00:00:00Z',
      lastLoginAt: new Date().toISOString(),
    }
  } else {
    throw new Error('Invalid credentials')
  }
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,

      login: async (credentials: { username: string; password: string }) => {
        set({ isLoading: true })
        try {
          const user = await mockLogin(credentials)
          set({ 
            user, 
            isAuthenticated: true, 
            isLoading: false 
          })
        } catch (error) {
          set({ isLoading: false })
          throw error
        }
      },

      logout: () => {
        set({ 
          user: null, 
          isAuthenticated: false, 
          isLoading: false 
        })
      },

      updateUser: (userData: Partial<User>) => {
        const { user } = get()
        if (user) {
          set({ 
            user: { ...user, ...userData } 
          })
        }
      },
    }),
    {
      name: 'auth-store',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        // 不持久化 isLoading
      }),
    }
  )
)