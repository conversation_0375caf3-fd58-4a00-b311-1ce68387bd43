'use client'

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { useTypedTranslation } from '@/hooks/use-typed-translation'
import { cn } from '@/lib/utils'
import { Bar<PERSON><PERSON> } from 'lucide-react'
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend
} from 'recharts'
import { useState, useMemo } from 'react'
import { useChartColors } from '@/hooks/use-chart-colors'

// 图表数据类型
interface ChartDataPoint {
  date: string
  requests: number
  success: number
  failed: number
}

interface TrendChartProps {
  data: ChartDataPoint[]
  loading?: boolean
  error?: string | null
  className?: string
}

// 时间范围选项
type TimeRange = '24h' | '7d' | '30d'

// 格式化日期显示
const formatDate = (dateStr: string, timeRange: TimeRange) => {
  const date = new Date(dateStr)

  switch (timeRange) {
    case '24h':
      return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    case '7d':
      return date.toLocaleDateString('zh-CN', {
        month: '2-digit',
        day: '2-digit'
      })
    case '30d':
      return date.toLocaleDateString('zh-CN', {
        month: '2-digit',
        day: '2-digit'
      })
    default:
      return dateStr
  }
}

// 自定义工具提示组件参数类型
interface TooltipPayload {
  dataKey: string
  value: number
  color: string
  name: string
}

interface CustomTooltipProps {
  active?: boolean
  payload?: TooltipPayload[]
  label?: string
}

// 自定义工具提示组件
const CustomTooltip = ({ active, payload, label }: CustomTooltipProps) => {
  if (active && payload && payload.length) {
    const successData = payload.find((p) => p.dataKey === 'success')
    const failedData = payload.find((p) => p.dataKey === 'failed')
    const total = (successData?.value || 0) + (failedData?.value || 0)
    const successRate = total > 0 ? ((successData?.value || 0) / total) * 100 : 0

    return (
      <div className="bg-background/98 backdrop-blur-md border border-border/60 rounded-2xl shadow-2xl p-5 min-w-[220px] max-w-[280px]">
        <div className="flex items-center gap-2 mb-4">
          <div className="w-2 h-2 rounded-full bg-primary"></div>
          <p className="font-semibold text-sm text-foreground">{label}</p>
        </div>

        <div className="space-y-3">
          {payload.map((entry, index: number) => (
            <div key={index} className="flex items-center justify-between group">
              <div className="flex items-center gap-3">
                <div
                  className="w-3 h-3 rounded-full shadow-sm"
                  style={{ backgroundColor: entry.color }}
                />
                <span className="text-sm font-medium text-foreground group-hover:text-primary transition-colors">
                  {entry.name}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm font-bold" style={{ color: entry.color }}>
                  {entry.value.toLocaleString()}
                </span>
                <span className="text-xs text-muted-foreground">次</span>
              </div>
            </div>
          ))}
        </div>

        {total > 0 && (
          <>
            <div className="my-3 border-t border-border/40"></div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-muted-foreground">总请求</span>
                <span className="text-sm font-bold text-foreground">
                  {total.toLocaleString()} 次
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-muted-foreground">成功率</span>
                <div className="flex items-center gap-2">
                  <div className="w-16 h-2 bg-muted rounded-full overflow-hidden">
                    <div
                      className="h-full bg-gradient-to-r from-green-500 to-emerald-500 rounded-full transition-all duration-300"
                      style={{ width: `${successRate}%` }}
                    />
                  </div>
                  <span className="text-sm font-bold text-emerald-600">
                    {successRate.toFixed(1)}%
                  </span>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    )
  }
  return null
}

export function TrendChart({
  data,
  loading,
  error,
  className
}: TrendChartProps) {
  const { t } = useTypedTranslation('dashboard')
  const [selectedTimeRange, setSelectedTimeRange] = useState<TimeRange>('24h')
  const chartColors = useChartColors()

  // 时间范围选项
  const timeRangeOptions: { value: TimeRange; label: string }[] = [
    { value: '24h', label: t('timeRange24h') },
    { value: '7d', label: t('timeRange7d') },
    { value: '30d', label: t('timeRange30d') }
  ]

  // 根据时间范围过滤数据
  const filteredData = useMemo(() => {
    if (!data || data.length === 0) return []

    const now = new Date()
    let startDate: Date

    switch (selectedTimeRange) {
      case '24h':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000)
        // 对于24小时，生成24个数据点
        return Array.from({ length: 24 }, (_, i) => {
          const baseSuccess = Math.floor(Math.random() * 80) + 15
          const baseFailed = Math.floor(Math.random() * 15) + 3
          return {
            date: `${String(i).padStart(2, '0')}:00`,
            requests: baseSuccess + baseFailed,
            success: baseSuccess,
            failed: baseFailed
          }
        })
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        return data.slice(-7).map(item => ({
          ...item,
          date: formatDate(item.date, selectedTimeRange)
        }))
      case '30d':
        return data.map(item => ({
          ...item,
          date: formatDate(item.date, selectedTimeRange)
        }))
      default:
        return data
    }
  }, [data, selectedTimeRange])


  if (loading) {
    return (
      <Card className={cn('relative overflow-hidden', className)}>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              {t('requestTrends')}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80 flex items-center justify-center">
            <div className="flex flex-col items-center space-y-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <p className="text-sm text-muted-foreground">加载中...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={cn('relative overflow-hidden border-red-200', className)}>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2 text-red-600">
              {t('requestTrends')}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80 flex items-center justify-center">
            <div className="text-center text-red-500">
              <BarChart className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-sm">{error}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!filteredData || filteredData.length === 0) {
    return (
      <Card className={cn('', className)}>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              {t('requestTrends')}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80 flex items-center justify-center">
            <div className="text-center text-muted-foreground">
              <BarChart className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-sm">暂无趋势数据</p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={cn('relative overflow-hidden hover:shadow-md transition-all duration-200', className)}>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">{t('requestTrends')}</h3>
          <div className="flex items-center gap-3">
            {/* 时间范围选择器 */}
            <Tabs value={selectedTimeRange} onValueChange={(value) => setSelectedTimeRange(value as TimeRange)}>
              <TabsList className="grid w-full grid-cols-3">
                {timeRangeOptions.map((option) => (
                  <TabsTrigger
                    key={option.value}
                    value={option.value}
                    className="cursor-pointer text-xs"
                  >
                    {option.label}
                  </TabsTrigger>
                ))}
              </TabsList>
            </Tabs>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="h-80 relative">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={filteredData}
              margin={{
                top: 20,
                right: 30,
                left: 20,
                bottom: 20,
              }}
            >
              <defs>
                <linearGradient id="successGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor={chartColors.success} stopOpacity={0.3} />
                  <stop offset="100%" stopColor={chartColors.success} stopOpacity={0.05} />
                </linearGradient>
                <linearGradient id="failedGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor={chartColors.error} stopOpacity={0.3} />
                  <stop offset="100%" stopColor={chartColors.error} stopOpacity={0.05} />
                </linearGradient>
                <filter id="glow">
                  <feGaussianBlur stdDeviation="3" result="coloredBlur" />
                  <feMerge>
                    <feMergeNode in="coloredBlur" />
                    <feMergeNode in="SourceGraphic" />
                  </feMerge>
                </filter>
              </defs>
              <CartesianGrid
                strokeDasharray="2 4"
                stroke={chartColors.border}
                opacity={0.3}
                horizontal={true}
                vertical={false}
              />
              <XAxis
                dataKey="date"
                axisLine={false}
                tickLine={false}
                tick={{
                  fontSize: 12,
                  fill: chartColors.foreground,
                  fontWeight: 500
                }}
                dy={10}
                interval={'preserveStartEnd'}
                tickFormatter={(value, index) => {
                  const dataLength = filteredData.length

                  if (selectedTimeRange === '24h') {
                    const hour = parseInt(value.split(':')[0])
                    return hour % 3 === 0 ? value : ''
                  } else if (selectedTimeRange === '7d') {
                    return value
                  } else if (selectedTimeRange === '30d') {
                    if (dataLength <= 10) {
                      return value
                    } else if (dataLength <= 20) {
                      return index % 2 === 0 ? value : ''
                    } else {
                      return index % 4 === 0 ? value : ''
                    }
                  }
                  return value
                }}
              />
              <YAxis
                axisLine={false}
                tickLine={false}
                tick={{
                  fontSize: 12,
                  fill: chartColors.foreground,
                  fontWeight: 500
                }}
                width={45}
                tickFormatter={(value) => {
                  return value.toString()
                }}
              />
              <Tooltip
                content={<CustomTooltip />}
                cursor={{
                  stroke: chartColors.foreground,
                  strokeWidth: 1,
                  strokeDasharray: '4 4',
                  opacity: 0.5
                }}
              />
              <Legend
                wrapperStyle={{
                  paddingTop: '30px',
                  paddingBottom: '0px',
                  fontSize: '13px',
                  fontWeight: 500,
                  color: chartColors.foreground
                }}
                iconType="circle"
                iconSize={8}
              />
              <Line
                type="monotone"
                dataKey="success"
                stroke={chartColors.success}
                strokeWidth={2}
                dot={{
                  fill: chartColors.success,
                  strokeWidth: 2,
                  r: 4,
                  stroke: 'hsl(var(--background))'
                }}
                activeDot={{
                  r: 3,
                  stroke: chartColors.success,
                  strokeWidth: 1,
                  fill: 'hsl(var(--background))'
                }}
                name="成功请求"
              />
              <Line
                type="monotone"
                dataKey="failed"
                stroke={chartColors.error}
                strokeWidth={2}
                dot={{
                  fill: chartColors.error,
                  strokeWidth: 2,
                  r: 4,
                  stroke: 'hsl(var(--background))'
                }}
                activeDot={{
                  r: 3,
                  stroke: chartColors.error,
                  strokeWidth: 1,
                  fill: 'hsl(var(--background))'
                }}
                name="失败请求"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  )
}