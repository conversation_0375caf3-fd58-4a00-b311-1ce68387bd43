'use client'

import React from 'react'
import { Filter, RotateCcw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { SearchBox } from '@/components/search-input'
import { DateRangePicker } from '@/components/date-range-picker'
import { StatusFilter } from '@/components/status-filter'
import { cn } from '@/lib/utils'
import type { DateRange, FilterOption } from '@/types/filters'

// 筛选工具栏配置
export interface FilterToolbarConfig {
  showSearch?: boolean
  showDateRange?: boolean
  showStatusFilter?: boolean
  showClearAll?: boolean
  statusFilterOptions?: FilterOption[]
  statusFilterMultiple?: boolean
  statusFilterPlaceholder?: string
  searchPlaceholder?: string
  dateRangePlaceholder?: string
}

// 筛选状态
export interface FilterState {
  search: string
  dateRange: DateRange
  status: unknown
}

// 筛选工具栏属性
export interface FilterToolbarProps {
  filters: FilterState
  onFiltersChange: (filters: FilterState) => void
  config?: FilterToolbarConfig
  className?: string
  disabled?: boolean
  onRefresh?: () => void
  isLoading?: boolean
}

export function FilterToolbar({
  filters,
  onFiltersChange,
  config = {},
  className,
  disabled = false,
  onRefresh,
  isLoading = false
}: FilterToolbarProps) {
  const {
    showSearch = true,
    showDateRange = true,
    showStatusFilter = true,
    showClearAll = true,
    statusFilterOptions = [],
    statusFilterMultiple = false,
    statusFilterPlaceholder = '选择状态',
    searchPlaceholder = '搜索...',
    dateRangePlaceholder = '选择日期范围'
  } = config

  // 处理搜索变化
  const handleSearchChange = (search: string) => {
    onFiltersChange({
      ...filters,
      search
    })
  }

  // 处理日期范围变化
  const handleDateRangeChange = (dateRange: DateRange) => {
    onFiltersChange({
      ...filters,
      dateRange
    })
  }

  // 处理状态筛选变化
  const handleStatusChange = (status: unknown) => {
    onFiltersChange({
      ...filters,
      status
    })
  }

  // 清除所有筛选
  const handleClearAll = () => {
    onFiltersChange({
      search: '',
      dateRange: { start: null, end: null },
      status: undefined
    })
  }

  // 检查是否有激活的筛选
  const hasActiveFilters = () => {
    return (
      filters.search !== '' ||
      filters.dateRange.start !== null ||
      filters.dateRange.end !== null ||
      filters.status !== undefined
    )
  }

  // 获取激活筛选的数量
  const getActiveFiltersCount = () => {
    let count = 0
    if (filters.search !== '') count++
    if (filters.dateRange.start || filters.dateRange.end) count++
    if (filters.status !== undefined) {
      if (Array.isArray(filters.status) && filters.status.length > 0) {
        count++
      } else if (!Array.isArray(filters.status)) {
        count++
      }
    }
    return count
  }

  const activeFiltersCount = getActiveFiltersCount()

  return (
    <div className={cn('space-y-4', className)}>
      {/* 主筛选区域 */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* 搜索框 */}
        {showSearch && (
          <div className="flex-1 min-w-0">
            <SearchBox
              value={filters.search}
              onChange={handleSearchChange}
              placeholder={searchPlaceholder}
              disabled={disabled}
              className="w-full"
            />
          </div>
        )}

        {/* 日期范围选择器 */}
        {showDateRange && (
          <div className="w-full sm:w-64">
            <DateRangePicker
              value={filters.dateRange}
              onChange={handleDateRangeChange}
              placeholder={dateRangePlaceholder}
              disabled={disabled}
              className="w-full"
            />
          </div>
        )}

        {/* 状态筛选器 */}
        {showStatusFilter && statusFilterOptions.length > 0 && (
          <div className="w-full sm:w-48">
            <StatusFilter
              options={statusFilterOptions}
              value={filters.status}
              onChange={handleStatusChange}
              placeholder={statusFilterPlaceholder}
              multiple={statusFilterMultiple}
              disabled={disabled}
              className="w-full"
            />
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex items-center gap-2">
          {/* 清除筛选按钮 */}
          {showClearAll && hasActiveFilters() && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleClearAll}
              disabled={disabled}
              className="whitespace-nowrap"
            >
              <Filter className="h-4 w-4 mr-2" />
              清除筛选
              {activeFiltersCount > 0 && (
                <span className="ml-1 bg-primary text-primary-foreground text-xs rounded-full px-1.5 py-0.5 min-w-[1.25rem] h-5 flex items-center justify-center">
                  {activeFiltersCount}
                </span>
              )}
            </Button>
          )}

          {/* 刷新按钮 */}
          {onRefresh && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRefresh}
              disabled={disabled || isLoading}
            >
              <RotateCcw className={cn(
                'h-4 w-4',
                isLoading && 'animate-spin'
              )} />
            </Button>
          )}
        </div>
      </div>

      {/* 激活筛选的状态显示 */}
      {hasActiveFilters() && (
        <div className="flex items-center justify-between text-sm text-muted-foreground bg-muted/30 px-3 py-2 rounded-md">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            <span>
              已应用 {activeFiltersCount} 个筛选条件
            </span>
          </div>
          
          {showClearAll && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClearAll}
              disabled={disabled}
              className="h-6 px-2 text-xs"
            >
              清除全部
            </Button>
          )}
        </div>
      )}
    </div>
  )
}