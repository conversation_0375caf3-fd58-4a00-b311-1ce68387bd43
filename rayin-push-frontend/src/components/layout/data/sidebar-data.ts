import {
  LayoutDashboard,
  Settings,
  Bell,
  FileText,
  Shield,
  Users,
  Activity,
  BarChart3,
  Webhook,
  Database,
  Building2,
  Zap
} from 'lucide-react'

export const sidebarData = {
  user: {
    username: "管理员",
    email: "<EMAIL>",
    role: "admin" as const
  },
  teams: [
    {
      name: "<PERSON><PERSON> Push",
      logo: Zap,
      plan: "企业版",
    },
    {
      name: "开发环境",
      logo: Building2,
      plan: "开发版",
    },
  ],
  navGroups: [
    {
      title: "主要功能",
      items: [
        {
          title: "仪表盘",
          url: "/dashboard",
          icon: LayoutDashboard,
          isActive: true,
        },
      ],
    },
    {
      title: "管理",
      items: [
        {
          title: "接口配置",
          url: "/config",
          icon: Settings,
        },
        {
          title: "通知渠道",
          url: "/channels",
          icon: Bell,
        },
        {
          title: "请求日志",
          url: "/logs",
          icon: FileText,
        },
        {
          title: "请求限制",
          url: "/limits",
          icon: Shield,
        },
      ],
    },
    {
      title: "系统",
      items: [
        {
          title: "用户管理",
          url: "/users",
          icon: Users,
        },
        {
          title: "系统监控",
          url: "/monitoring",
          icon: Activity,
          items: [
            {
              title: "数据分析",
              url: "/monitoring/analytics",
              icon: BarChart3,
            },
            {
              title: "Webhook管理",
              url: "/monitoring/webhooks",
              icon: Webhook,
            },
            {
              title: "数据库状态",
              url: "/monitoring/database",
              icon: Database,
            },
          ],
        },
      ],
    },
  ],
}