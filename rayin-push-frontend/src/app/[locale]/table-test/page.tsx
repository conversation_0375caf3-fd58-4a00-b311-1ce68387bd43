'use client'

import React, { useState } from 'react'
import { AppLayout } from '@/components/app-layout'
import { ResponsiveDataTable } from '@/components/responsive-data-table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { useTypedTranslation } from '@/hooks/use-typed-translation'
import { useStoreInitialization } from '@/hooks/use-store'
import type { DataTableColumn } from '@/types/dataTable'

// 示例数据类型
interface ExampleData extends Record<string, unknown> {
  id: number
  name: string
  email: string
  status: 'active' | 'inactive' | 'pending'
  role: 'admin' | 'user'
  createdTime: string
  loginCount: number
}

export default function DataTableTestPage() {
  const { isLoading: translationLoading } = useTypedTranslation('common')
  const { isHydrated } = useStoreInitialization()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 示例数据
  const [data, setData] = useState<ExampleData[]>([
    {
      id: 1,
      name: '张三',
      email: '<EMAIL>',
      status: 'active',
      role: 'admin',
      createdTime: '2024-01-15',
      loginCount: 42
    },
    {
      id: 2,
      name: '李四',
      email: '<EMAIL>',
      status: 'inactive',
      role: 'user',
      createdTime: '2024-02-20',
      loginCount: 15
    },
    {
      id: 3,
      name: '王五',
      email: '<EMAIL>',
      status: 'pending',
      role: 'user',
      createdTime: '2024-03-10',
      loginCount: 3
    },
    {
      id: 4,
      name: '赵六',
      email: '<EMAIL>',
      status: 'active',
      role: 'user',
      createdTime: '2024-03-25',
      loginCount: 28
    },
    {
      id: 5,
      name: '钱七',
      email: '<EMAIL>',
      status: 'active',
      role: 'admin',
      createdTime: '2024-04-01',
      loginCount: 67
    }
  ])

  // 表格列定义
  const columns: DataTableColumn<ExampleData>[] = [
    {
      id: 'name',
      header: '用户名',
      accessorKey: 'name',
      sortable: true,
      width: '150px'
    },
    {
      id: 'email',
      header: '邮箱',
      accessorKey: 'email',
      sortable: true,
      width: '200px'
    },
    {
      id: 'status',
      header: '状态',
      cell: (row) => {
        const statusMap = {
          active: { label: '活跃', variant: 'default' as const },
          inactive: { label: '非活跃', variant: 'secondary' as const },
          pending: { label: '待激活', variant: 'outline' as const }
        }
        const status = statusMap[row.status]
        return <Badge variant={status.variant}>{status.label}</Badge>
      },
      width: '100px'
    },
    {
      id: 'role',
      header: '角色',
      cell: (row) => {
        const roleMap = {
          admin: { label: '管理员', variant: 'destructive' as const },
          user: { label: '用户', variant: 'default' as const }
        }
        const role = roleMap[row.role]
        return <Badge variant={role.variant}>{role.label}</Badge>
      },
      width: '80px'
    },
    {
      id: 'loginCount',
      header: '登录次数',
      accessorKey: 'loginCount',
      sortable: true,
      align: 'right' as const,
      width: '100px'
    },
    {
      id: 'createdTime',
      header: '创建时间',
      accessorKey: 'createdTime',
      sortable: true,
      width: '120px'
    },
    {
      id: 'actions',
      header: '操作',
      cell: () => (
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            编辑
          </Button>
          <Button variant="outline" size="sm" className="text-destructive">
            删除
          </Button>
        </div>
      ),
      width: '120px'
    }
  ]

  // 模拟加载数据
  const handleRefresh = () => {
    setIsLoading(true)
    setError(null)
    
    setTimeout(() => {
      // 模拟可能的错误
      if (Math.random() < 0.1) {
        setError('加载数据失败，请重试')
        setIsLoading(false)
        return
      }
      
      // 模拟数据更新
      setData(prevData => 
        prevData.map(item => ({
          ...item,
          loginCount: item.loginCount + Math.floor(Math.random() * 5)
        }))
      )
      setIsLoading(false)
    }, 1000)
  }

  if (translationLoading || !isHydrated) {
    return (
      <AppLayout title="数据表格测试">
        <div className="flex items-center justify-center h-full">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </AppLayout>
    )
  }

  return (
    <AppLayout title="数据表格测试">
      <div className="p-4 space-y-4">
        <div>
          <h1 className="text-2xl font-bold mb-2">数据表格组件测试</h1>
          <p className="text-muted-foreground">
            这是一个响应式数据表格组件的演示页面，支持排序、搜索、分页等功能。
          </p>
        </div>

        <ResponsiveDataTable<ExampleData>
          columns={columns}
          data={data}
          loading={{ isLoading, error }}
          config={{
            enableSorting: true,
            enableFiltering: true,
            enablePagination: true,
            pageSize: 5,
            pageSizeOptions: [5, 10, 20]
          }}
          actions={{
            onRefresh: handleRefresh,
            onSortingChange: (sorting) => {
              console.log('Sorting changed:', sorting)
            },
            onPaginationChange: (pagination) => {
              console.log('Pagination changed:', pagination)
            }
          }}
          emptyMessage="没有找到用户数据"
        />
      </div>
    </AppLayout>
  )
}