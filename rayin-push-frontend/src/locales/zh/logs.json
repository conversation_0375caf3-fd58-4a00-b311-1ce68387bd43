{"title": "请求日志", "logDetails": "日志详情", "requestInfo": "请求信息", "responseInfo": "响应信息", "channelResults": "渠道结果", "originalData": "原始数据", "processedData": "处理后数据", "errorInfo": "错误信息", "timeFilter": "时间筛选", "statusFilter": "状态筛选", "interfaceFilter": "接口筛选", "allStatus": "全部状态", "allInterfaces": "全部接口", "requestId": "请求ID", "interfaceName": "接口名称", "requestTime": "请求时间", "responseTime": "响应时间", "duration": "耗时", "requestStatus": "请求状态", "channelsCount": "渠道数量", "successfulChannels": "成功渠道", "failedChannels": "失败渠道", "exportLogs": "导出日志", "exportCsv": "导出CSV", "exportJson": "导出JSON", "exportSelected": "导出选中", "exportAll": "导出全部", "exportProgress": "导出进度", "searchLogs": "搜索日志", "searchPlaceholder": "搜索请求ID、接口名称...", "dateRange": "日期范围", "today": "今天", "yesterday": "昨天", "last7Days": "最近7天", "last30Days": "最近30天", "customRange": "自定义范围"}