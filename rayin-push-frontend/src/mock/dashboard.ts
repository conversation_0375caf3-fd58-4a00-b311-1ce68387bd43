import type { DashboardStats } from '@/types/data'
import { mockRequestLogs } from './logs'

// 生成趋势数据
const generateTrendData = () => {
  const trends = []
  const now = new Date()
  
  for (let i = 29; i >= 0; i--) {
    const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000)
    // 生成更真实的请求数据：工作日更多，周末较少
    const dayOfWeek = date.getDay()
    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6
    
    const baseRequests = isWeekend ? 200 : 800
    const variance = Math.floor(Math.random() * 400) - 200
    const requests = Math.max(50, baseRequests + variance)
    
    const successRate = 0.88 + Math.random() * 0.1 // 88%-98%成功率
    const success = Math.floor(requests * successRate)
    const failed = requests - success

    trends.push({
      date: date.toISOString().split('T')[0],
      requests,
      success,
      failed
    })
  }
  
  return trends
}

export const mockDashboardStats: DashboardStats = {
  totalUsers: 127,
  totalRequests: 45678,
  successRate: 94.2,
  todayRequests: 1249,
  activeConfigs: 12,
  activeChannels: 8,
  recentRequests: mockRequestLogs.slice(0, 10), // 最近10条请求
  requestTrends: generateTrendData()
}

// 实时更新仪表盘数据的函数
export const updateDashboardStats = (): DashboardStats => {
  const baseStats = mockDashboardStats
  
  return {
    ...baseStats,
    totalRequests: baseStats.totalRequests + Math.floor(Math.random() * 50),
    successRate: Math.max(80, Math.min(98, baseStats.successRate + (Math.random() * 4 - 2))),
    todayRequests: baseStats.todayRequests + Math.floor(Math.random() * 20),
    recentRequests: [
      // 添加一个新的模拟请求到顶部
      {
        id: `new-${Date.now()}`,
        interfaceId: '1',
        interfaceName: '用户注册通知',
        requestTime: new Date().toISOString(),
        responseTime: new Date(Date.now() + 1000).toISOString(),
        duration: 1000 + Math.floor(Math.random() * 2000),
        status: Math.random() > 0.2 ? 'success' : 'failed',
        originalData: { sample: 'data' },
        processedData: { processed: 'data' },
        channelResults: [
          {
            channelId: '1',
            channelName: '开发团队微信群',
            status: 'success',
            response: 'ok',
            sentAt: new Date().toISOString()
          }
        ],
        clientIp: '192.168.1.' + Math.floor(Math.random() * 255),
        userAgent: 'Test/1.0'
      },
      ...baseStats.recentRequests.slice(0, 9)
    ]
  }
}