'use client'

import { useState, useEffect, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { useTypedTranslation } from '@/hooks/use-typed-translation'
import { mockNotificationChannels } from '@/mock/channels'
import { StatusFilter } from '@/components/status-filter'
import { ParsingRules } from './parsing-rules'
import type { InterfaceConfig } from '@/types/data'
import { Save, X } from 'lucide-react'

interface ConfigEditModalProps {
  config: InterfaceConfig | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onSave: (config: InterfaceConfig) => void
}

export function ConfigEditModal({
  config,
  open,
  onOpenChange,
  onSave
}: ConfigEditModalProps) {
  const { t } = useTypedTranslation('config')

  // 名称输入框引用，用于自动聚焦
  const nameInputRef = useRef<HTMLInputElement>(null)

  // 表单状态
  const [formData, setFormData] = useState<InterfaceConfig>(
    config || {
      id: '',
      name: '',
      token: '',
      description: '',
      method: 'GET' as const,
      status: 'enabled',
      channels: [],
      parsingRules: {
        type: 'post-json',
        variableMapping: {}
      },
      createdTime: '',
      updatedTime: ''
    }
  )

  // 当 config 变化时更新表单数据
  useEffect(() => {
    if (config) {
      setFormData(config)
    } else {
      // 重置为默认值
      setFormData({
        id: '',
        name: '',
        token: '',
        description: '',
        method: 'GET' as const,
        status: 'enabled',
        channels: [],
        parsingRules: {
          type: 'post-json',
          variableMapping: {}
        },
        createdTime: '',
        updatedTime: ''
      })
    }
    setErrors({}) // 清除错误
  }, [config])

  // 当模态框打开时，自动聚焦到名称输入框
  useEffect(() => {
    if (open && nameInputRef.current) {
      // 使用 setTimeout 确保 DOM 已经渲染完成
      const timer = setTimeout(() => {
        nameInputRef.current?.focus()
      }, 100)

      return () => clearTimeout(timer)
    }
  }, [open])

  // 表单验证错误
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [loading, setLoading] = useState(false)
  
  // 复制状态
  const [copiedToken, setCopiedToken] = useState(false)

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = t('configNameRequired')
    }

    if (!formData.method) {
      newErrors.method = t('requestMethodRequired')
    }

    if (formData.channels.length === 0) {
      newErrors.channels = t('channelRequired')
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 处理表单提交
  const handleSubmit = async () => {
    if (validateForm()) {
      setLoading(true)
      try {
        // 模拟API延迟
        await new Promise(resolve => setTimeout(resolve, 1000))
        onSave(formData)
        onOpenChange(false)
      } catch (error) {
        console.error('Failed to save config:', error)
      } finally {
        setLoading(false)
      }
    }
  }

  // 处理提取规则更新
  const handleParsingRulesChange = (rules: InterfaceConfig['parsingRules']) => {
    setFormData(prev => ({
      ...prev,
      parsingRules: rules
    }))
  }

  // 获取活跃的渠道列表并转换为 StatusFilter 需要的格式
  const channelOptions = mockNotificationChannels
    .filter(channel => channel.status === 'active')
    .map(channel => ({
      value: channel.id,
      label: channel.name,
      count: undefined,
      data: channel as unknown as Record<string, unknown> // 保存完整的渠道数据
    }))

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {config ? t('editConfig') : t('createConfig')}
          </DialogTitle>
          <DialogDescription>
            {config ? t('editConfigDesc') : t('createConfigDesc')}
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="basic" className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-6">
            <TabsTrigger value="basic" className="cursor-pointer">{t('basicInfo')}</TabsTrigger>
            <TabsTrigger 
              value="rules" 
              disabled={!formData.method}
              className={!formData.method ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
            >
              {t('parsingRules')}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            {/* 配置名称 */}
            <div className="space-y-2">
              <Label htmlFor="name">
                {t('configName')} <span className="text-red-500">*</span>
              </Label>
              <Input
                ref={nameInputRef}
                id="name"
                value={formData.name}
                onChange={(e) => {
                  setFormData(prev => ({ ...prev, name: e.target.value }))
                  if (errors.name) {
                    const newErrors = { ...errors }
                    delete newErrors.name
                    setErrors(newErrors)
                  }
                }}
                placeholder={t('configNamePlaceholder')}
                className={errors.name ? 'border-red-500' : ''}
              />
              {errors.name && (
                <p className="text-sm text-red-500">{errors.name}</p>
              )}
            </div>

            {/* 配置描述 */}
            <div className="space-y-2">
              <Label htmlFor="description">
                {t('configDescription')}
              </Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => {
                  setFormData(prev => ({ ...prev, description: e.target.value }))
                  if (errors.description) {
                    const newErrors = { ...errors }
                    delete newErrors.description
                    setErrors(newErrors)
                  }
                }}
                placeholder={t('configDescPlaceholder')}
                rows={3}
                className={errors.description ? 'border-red-500' : ''}
              />
              {errors.description && (
                <p className="text-sm text-red-500">{errors.description}</p>
              )}
            </div>

            {/* 请求方式 */}
            <div className="space-y-2">
              <Label>{t('requestMethod')} <span className="text-red-500">*</span></Label>
              <Select 
                value={formData.method} 
                onValueChange={(value: 'GET' | 'POST') => {
                  setFormData(prev => ({ ...prev, method: value }))
                  if (errors.method) {
                    const newErrors = { ...errors }
                    delete newErrors.method
                    setErrors(newErrors)
                  }
                }}
              >
                <SelectTrigger className={`w-full cursor-pointer ${errors.method ? 'border-red-500' : ''}`}>
                  <SelectValue placeholder={t('selectMethodPlaceholder')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="GET">GET</SelectItem>
                  <SelectItem value="POST">POST</SelectItem>
                </SelectContent>
              </Select>
              {errors.method && (
                <p className="text-sm text-red-500">{errors.method}</p>
              )}
            </div>

            {/* 推送渠道设置 */}
            <div className="space-y-2">
              <Label>
                {t('channelSettings')} <span className="text-red-500">*</span>
              </Label>
              <StatusFilter
                options={channelOptions}
                value={formData.channels}
                onChange={(value) => {
                  setFormData(prev => ({ 
                    ...prev, 
                    channels: value ? (Array.isArray(value) ? value : [value]) : []
                  }))
                  // 清除渠道相关错误
                  if (errors.channels) {
                    const newErrors = { ...errors }
                    delete newErrors.channels
                    setErrors(newErrors)
                  }
                }}
                placeholder={t('selectChannelPlaceholder')}
                multiple={true}
                className={errors.channels ? 'border-red-500 cursor-pointer' : 'cursor-pointer'}
                showClearButton={true}
                showSelectedItems={false}
                renderOption={(option, isSelected) => (
                  <div className="flex-1 space-y-1 min-w-0">
                    <div className="flex items-center gap-2 min-w-0">
                      <span 
                        className="font-medium truncate flex-1 min-w-0 max-w-[200px]" 
                        title={(option.data as Record<string, unknown>)?.name as string}
                      >
                        {(option.data as Record<string, unknown>)?.name as string}
                      </span>
                      <Badge variant="outline" className="text-xs flex-shrink-0">
                        {(option.data as Record<string, unknown>)?.type as string}
                      </Badge>
                    </div>
                    <p 
                      className="text-sm text-muted-foreground truncate max-w-[250px]"
                      title={(option.data as Record<string, unknown>)?.description as string}
                    >
                      {(option.data as Record<string, unknown>)?.description as string}
                    </p>
                  </div>
                )}
              />
              {errors.channels && (
                <p className="text-sm text-red-500">{errors.channels}</p>
              )}
            </div>
          </TabsContent>

          <TabsContent value="rules">
            {formData.method ? (
              <ParsingRules
                method={formData.method}
                rules={formData.parsingRules}
                onChange={handleParsingRulesChange}
              />
            ) : (
              <div className="flex items-center justify-center py-12 text-muted-foreground">
                <div className="text-center">
                  <p className="text-lg mb-2">{t('selectMethodFirst')}</p>
                  <p className="text-sm">{t('selectMethodHint')}</p>
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={loading}
            className="cursor-pointer"
          >
            <X className="h-4 w-4 mr-1" />
            {t('cancel')}
          </Button>
          
          <Button
            onClick={handleSubmit}
            disabled={loading}
            className="cursor-pointer"
          >
            <Save className="h-4 w-4 mr-1" />
            {loading ? t('saving') : t('save')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}