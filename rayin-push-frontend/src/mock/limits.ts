import type { RateLimit } from '@/types/data'

export const mockRateLimits: RateLimit[] = [
  {
    id: '1',
    name: '全局请求限制',
    description: '全局每分钟最多1000个请求',
    type: 'global',
    timeWindow: 60, // 60秒
    requestLimit: 1000,
    status: 'enabled',
    createdTime: '2024-01-10T10:00:00Z',
    updatedTime: '2024-12-20T15:30:00Z',
    triggeredCount: 23,
    lastTriggered: '2024-12-24T14:35:20Z',
    creator: 'admin'
  },
  {
    id: '2',
    name: '单IP限制-严格',
    description: '单个IP每分钟最多50个请求',
    type: 'per-ip',
    timeWindow: 60,
    requestLimit: 50,
    status: 'enabled',
    ipRules: {
      type: 'blacklist',
      ips: ['*************', '**********', '***********/24']
    },
    createdTime: '2024-01-15T11:30:00Z',
    updatedTime: '2024-12-18T09:20:00Z',
    triggeredCount: 156,
    lastTriggered: '2024-12-25T08:45:15Z',
    creator: 'admin'
  },
  {
    id: '3',
    name: '开发环境宽松限制',
    description: '开发环境每小时最多10000个请求',
    type: 'global',
    timeWindow: 3600, // 1小时
    requestLimit: 10000,
    status: 'enabled',
    ipRules: {
      type: 'whitelist',
      ips: ['***********/24', '********/24']
    },
    createdTime: '2024-02-01T09:00:00Z',
    updatedTime: '2024-11-30T16:45:00Z',
    triggeredCount: 5,
    lastTriggered: '2024-11-28T13:20:30Z',
    creator: 'zhang_wei'
  },
  {
    id: '4',
    name: '单用户限制',
    description: '单个用户每小时最多500个请求',
    type: 'per-user',
    timeWindow: 3600,
    requestLimit: 500,
    status: 'enabled',
    createdTime: '2024-03-10T14:20:00Z',
    updatedTime: '2024-12-15T11:10:00Z',
    triggeredCount: 78,
    lastTriggered: '2024-12-23T16:22:45Z',
    creator: 'li_ming'
  },
  {
    id: '5',
    name: '紧急限制-暂停',
    description: '紧急情况下的严格限制（已暂停）',
    type: 'per-ip',
    timeWindow: 60,
    requestLimit: 10,
    status: 'disabled',
    ipRules: {
      type: 'blacklist',
      ips: ['0.0.0.0/0'] // 阻止所有IP
    },
    createdTime: '2024-06-15T18:30:00Z',
    updatedTime: '2024-08-20T10:15:00Z',
    triggeredCount: 0,
    creator: 'admin'
  },
  {
    id: '6',
    name: '爬虫防护',
    description: '防止爬虫过度请求',
    type: 'per-ip',
    timeWindow: 300, // 5分钟
    requestLimit: 100,
    status: 'enabled',
    ipRules: {
      type: 'blacklist',
      ips: [
        '*******',
        '*******',
        '*************',
        '************/24'
      ]
    },
    createdTime: '2024-05-20T12:00:00Z',
    updatedTime: '2024-12-10T14:30:00Z',
    triggeredCount: 342,
    lastTriggered: '2024-12-25T09:15:33Z',
    creator: 'admin'
  }
]