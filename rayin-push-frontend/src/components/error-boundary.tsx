'use client'

import React from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { AlertTriangle, RefreshCw } from 'lucide-react'

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
  errorInfo?: React.ErrorInfo
}

interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<{ error: Error; reset: () => void }>
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true,
      error,
    }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo)
    this.setState({
      error,
      errorInfo,
    })
  }

  handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined })
  }

  render() {
    if (this.state.hasError && this.state.error) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback
        return <FallbackComponent error={this.state.error} reset={this.handleReset} />
      }

      return <DefaultErrorFallback error={this.state.error} reset={this.handleReset} />
    }

    return this.props.children
  }
}

// 默认错误回退组件
export function DefaultErrorFallback({ error, reset }: { error: Error; reset: () => void }) {
  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-md p-6 text-center">
        <AlertTriangle className="h-12 w-12 text-destructive mx-auto mb-4" />
        <h2 className="text-lg font-semibold mb-2">出现了错误</h2>
        <p className="text-sm text-muted-foreground mb-4">
          抱歉，应用程序遇到了意外错误。
        </p>
        
        <details className="text-left mb-4">
          <summary className="text-sm font-medium cursor-pointer hover:text-primary">
            查看错误详情
          </summary>
          <pre className="mt-2 text-xs bg-muted p-2 rounded overflow-auto max-h-32">
            {error.message}
            {error.stack && `\n\n${error.stack}`}
          </pre>
        </details>

        <div className="flex gap-2 justify-center">
          <Button onClick={reset} size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            重试
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => window.location.reload()}
          >
            刷新页面
          </Button>
        </div>
      </Card>
    </div>
  )
}

// 页面级错误回退组件
export function PageErrorFallback({ error, reset }: { error: Error; reset: () => void }) {
  return (
    <div className="flex flex-col items-center justify-center min-h-[400px] p-8">
      <AlertTriangle className="h-16 w-16 text-destructive mb-4" />
      <h2 className="text-xl font-semibold mb-2">页面加载失败</h2>
      <p className="text-muted-foreground mb-6 text-center max-w-md">
        页面在加载时遇到了问题。请尝试刷新页面或稍后再试。
      </p>
      
      <div className="flex gap-3">
        <Button onClick={reset}>
          <RefreshCw className="h-4 w-4 mr-2" />
          重新加载
        </Button>
        <Button variant="outline" onClick={() => window.history.back()}>
          返回上一页
        </Button>
      </div>
      
      <details className="mt-6 text-center">
        <summary className="text-sm text-muted-foreground cursor-pointer hover:text-primary">
          技术详情
        </summary>
        <pre className="mt-2 text-xs bg-muted p-3 rounded text-left overflow-auto max-w-md">
          {error.message}
        </pre>
      </details>
    </div>
  )
}