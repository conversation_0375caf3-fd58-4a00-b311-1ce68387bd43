import type { Metadata, Viewport } from 'next'
import { ReactNode } from 'react'
import { notFound } from 'next/navigation'
import { PageWrapper } from '@/components/providers'
import { LoadingBar } from '@/components/ui/loading-bar'
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar'
import { AppSidebar } from '@/components/app-sidebar'

const locales = ['zh', 'en']

export async function generateStaticParams() {
  return locales.map((locale) => ({ locale }))
}

// 导出viewport配置
export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
}

// 动态生成元数据
export async function generateMetadata({ 
  params 
}: { 
  params: Promise<{ locale: string }> 
}): Promise<Metadata> {
  const { locale } = await params
  
  const titles = {
    zh: 'Rayin Push - 消息推送通知系统',
    en: 'Rayin Push - Message Push System'
  }
  
  const descriptions = {
    zh: '强大的消息推送通知系统，支持多渠道消息推送和智能路由',
    en: 'Powerful message push notification system with multi-channel support and intelligent routing'
  }

  return {
    title: titles[locale as keyof typeof titles] || titles.zh,
    description: descriptions[locale as keyof typeof descriptions] || descriptions.zh,
    openGraph: {
      title: titles[locale as keyof typeof titles] || titles.zh,
      description: descriptions[locale as keyof typeof descriptions] || descriptions.zh,
      type: 'website',
      locale: locale,
      alternateLocale: locales.filter(l => l !== locale),
    },
  }
}

interface LocaleLayoutProps {
  children: ReactNode
  params: Promise<{ locale: string }>
}

export default async function LocaleLayout({
  children,
  params,
}: LocaleLayoutProps) {
  const { locale } = await params
  
  // 验证 locale 是否有效
  if (!locales.includes(locale)) {
    notFound()
  }

  return (
    <PageWrapper>
      {/* 全局加载条 - 覆盖整个页面顶部 */}
      <LoadingBar />
      <div id="root" className="relative flex min-h-screen flex-col">
        <SidebarProvider defaultOpen={true}>
          <AppSidebar />
          <SidebarInset>
            {children}
          </SidebarInset>
        </SidebarProvider>
      </div>
    </PageWrapper>
  )
}