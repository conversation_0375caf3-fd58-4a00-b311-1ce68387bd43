import type { 
  UserData, 
  UserOperationLog, 
  InterfaceConfig, 
  NotificationChannel, 
  RequestLog, 
  RateLimit, 
  DashboardStats 
} from '@/types/data'

import { mockUsers, mockUserOperationLogs } from './users'
import { mockInterfaceConfigs } from './configs'
import { mockNotificationChannels } from './channels'
import { mockRequestLogs, generateMoreLogs } from './logs'
import { mockRateLimits } from './limits'
import { mockDashboardStats, updateDashboardStats } from './dashboard'

// 模拟网络延迟
const delay = (ms: number = 500) => new Promise(resolve => setTimeout(resolve, ms))

// 分页参数类型
interface PaginationParams {
  page?: number
  pageSize?: number
  search?: string
  status?: string
  sort?: string
  order?: 'asc' | 'desc'
}

// 分页结果类型
interface PaginatedResult<T> {
  data: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 通用分页函数
function paginate<T>(
  data: T[], 
  params: PaginationParams,
  searchFields: (keyof T)[] = []
): PaginatedResult<T> {
  const { page = 1, pageSize = 10, search = '', sort, order = 'desc' } = params
  
  let filteredData = [...data]
  
  // 搜索过滤
  if (search && searchFields.length > 0) {
    filteredData = filteredData.filter(item =>
      searchFields.some(field => 
        String(item[field]).toLowerCase().includes(search.toLowerCase())
      )
    )
  }
  
  // 状态过滤
  if (params.status && params.status !== 'all') {
    filteredData = filteredData.filter(item => {
      const itemStatus = (item as { status?: string }).status
      return itemStatus === params.status
    })
  }
  
  // 排序
  if (sort) {
    filteredData.sort((a, b) => {
      const aValue = (a as Record<string, unknown>)[sort]
      const bValue = (b as Record<string, unknown>)[sort]
      
      // 类型安全的比较
      const aStr = String(aValue)
      const bStr = String(bValue)
      
      if (order === 'asc') {
        return aStr > bStr ? 1 : -1
      } else {
        return aStr < bStr ? 1 : -1
      }
    })
  }
  
  const total = filteredData.length
  const totalPages = Math.ceil(total / pageSize)
  const startIndex = (page - 1) * pageSize
  const endIndex = startIndex + pageSize
  
  return {
    data: filteredData.slice(startIndex, endIndex),
    total,
    page,
    pageSize,
    totalPages
  }
}

// 用户管理API
export const userApi = {
  // 获取用户列表
  async getUsers(params: PaginationParams = {}): Promise<PaginatedResult<UserData>> {
    await delay()
    return paginate(mockUsers, params, ['username', 'email', 'role'])
  },

  // 获取单个用户
  async getUser(id: string): Promise<UserData | null> {
    await delay()
    return mockUsers.find(user => user.id === id) || null
  },

  // 创建用户
  async createUser(userData: Omit<UserData, 'id' | 'createdTime' | 'loginCount'>): Promise<UserData> {
    await delay()
    const newUser: UserData = {
      ...userData,
      id: `user-${Date.now()}`,
      createdTime: new Date().toISOString(),
      loginCount: 0
    }
    mockUsers.push(newUser)
    return newUser
  },

  // 更新用户
  async updateUser(id: string, userData: Partial<UserData>): Promise<UserData | null> {
    await delay()
    const userIndex = mockUsers.findIndex(user => user.id === id)
    if (userIndex === -1) return null
    
    mockUsers[userIndex] = { ...mockUsers[userIndex], ...userData }
    return mockUsers[userIndex]
  },

  // 删除用户
  async deleteUser(id: string): Promise<boolean> {
    await delay()
    const userIndex = mockUsers.findIndex(user => user.id === id)
    if (userIndex === -1) return false
    
    mockUsers.splice(userIndex, 1)
    return true
  },

  // 获取用户操作日志
  async getUserOperationLogs(params: PaginationParams = {}): Promise<PaginatedResult<UserOperationLog>> {
    await delay()
    return paginate(mockUserOperationLogs, params, ['username', 'operation', 'target'])
  }
}

// 接口配置API
export const configApi = {
  // 获取配置列表
  async getConfigs(params: PaginationParams = {}): Promise<PaginatedResult<InterfaceConfig>> {
    await delay()
    return paginate(mockInterfaceConfigs, params, ['name', 'description'])
  },

  // 获取单个配置
  async getConfig(id: string): Promise<InterfaceConfig | null> {
    await delay()
    return mockInterfaceConfigs.find(config => config.id === id) || null
  },

  // 创建配置
  async createConfig(configData: Omit<InterfaceConfig, 'id' | 'createdTime' | 'updatedTime'>): Promise<InterfaceConfig> {
    await delay()
    const newConfig: InterfaceConfig = {
      ...configData,
      id: `config-${Date.now()}`,
      createdTime: new Date().toISOString(),
      updatedTime: new Date().toISOString(),
    }
    mockInterfaceConfigs.push(newConfig)
    return newConfig
  },

  // 更新配置
  async updateConfig(id: string, configData: Partial<InterfaceConfig>): Promise<InterfaceConfig | null> {
    await delay()
    const configIndex = mockInterfaceConfigs.findIndex(config => config.id === id)
    if (configIndex === -1) return null
    
    mockInterfaceConfigs[configIndex] = {
      ...mockInterfaceConfigs[configIndex],
      ...configData,
      updatedTime: new Date().toISOString()
    }
    return mockInterfaceConfigs[configIndex]
  },

  // 删除配置
  async deleteConfig(id: string): Promise<boolean> {
    await delay()
    const configIndex = mockInterfaceConfigs.findIndex(config => config.id === id)
    if (configIndex === -1) return false
    
    mockInterfaceConfigs.splice(configIndex, 1)
    return true
  },

  // 测试配置
  async testConfig(id: string, testData: unknown): Promise<{ success: boolean; message: string; data?: unknown }> {
    await delay(1500) // 测试需要更长时间
    const config = mockInterfaceConfigs.find(c => c.id === id)
    if (!config) {
      return { success: false, message: '配置不存在' }
    }
    
    // 模拟测试结果
    const success = Math.random() > 0.2 // 80%成功率
    return {
      success,
      message: success ? '测试成功' : '测试失败：连接超时',
      data: success ? { processed: testData } : undefined
    }
  }
}

// 通知渠道API
export const channelApi = {
  // 获取渠道列表
  async getChannels(params: PaginationParams = {}): Promise<PaginatedResult<NotificationChannel>> {
    await delay()
    return paginate(mockNotificationChannels, params, ['name', 'type', 'description'])
  },

  // 获取单个渠道
  async getChannel(id: string): Promise<NotificationChannel | null> {
    await delay()
    return mockNotificationChannels.find(channel => channel.id === id) || null
  },

  // 创建渠道
  async createChannel(channelData: Omit<NotificationChannel, 'id' | 'createdTime' | 'updatedTime'>): Promise<NotificationChannel> {
    await delay()
    const newChannel: NotificationChannel = {
      ...channelData,
      id: `channel-${Date.now()}`,
      createdTime: new Date().toISOString(),
      updatedTime: new Date().toISOString()
    }
    mockNotificationChannels.push(newChannel)
    return newChannel
  },

  // 更新渠道
  async updateChannel(id: string, channelData: Partial<NotificationChannel>): Promise<NotificationChannel | null> {
    await delay()
    const channelIndex = mockNotificationChannels.findIndex(channel => channel.id === id)
    if (channelIndex === -1) return null
    
    mockNotificationChannels[channelIndex] = {
      ...mockNotificationChannels[channelIndex],
      ...channelData,
      updatedTime: new Date().toISOString()
    }
    return mockNotificationChannels[channelIndex]
  },

  // 删除渠道
  async deleteChannel(id: string): Promise<boolean> {
    await delay()
    const channelIndex = mockNotificationChannels.findIndex(channel => channel.id === id)
    if (channelIndex === -1) return false
    
    mockNotificationChannels.splice(channelIndex, 1)
    return true
  },

  // 测试渠道
  async testChannel(id: string, _message: string): Promise<{ success: boolean; message: string; response?: string }> {
    await delay(1000)
    const channel = mockNotificationChannels.find(c => c.id === id)
    if (!channel) {
      return { success: false, message: '渠道不存在' }
    }
    
    if (channel.status === 'inactive') {
      return { success: false, message: '渠道未激活' }
    }
    
    // 模拟测试结果
    const success = Math.random() > 0.15 // 85%成功率
    
    // 更新测试状态
    const channelIndex = mockNotificationChannels.findIndex(c => c.id === id)
    if (channelIndex !== -1) {
      mockNotificationChannels[channelIndex] = {
        ...mockNotificationChannels[channelIndex],
        lastTestAt: new Date().toISOString(),
        testStatus: success ? 'success' : 'failed'
      }
    }
    
    return {
      success,
      message: success ? '测试成功' : '测试失败：网络错误',
      response: success ? 'Message sent successfully' : undefined
    }
  }
}

// 请求日志API
export const logApi = {
  // 获取日志列表
  async getLogs(params: PaginationParams & { 
    interfaceId?: string
    startDate?: string
    endDate?: string 
  } = {}): Promise<PaginatedResult<RequestLog>> {
    await delay()
    
    // 合并真实数据和生成的数据
    const allLogs = [...mockRequestLogs, ...generateMoreLogs(100)]
    
    let filteredLogs = allLogs
    
    // 接口过滤
    if (params.interfaceId && params.interfaceId !== 'all') {
      filteredLogs = filteredLogs.filter(log => log.interfaceId === params.interfaceId)
    }
    
    // 日期范围过滤
    if (params.startDate) {
      filteredLogs = filteredLogs.filter(log => log.requestTime >= params.startDate!)
    }
    if (params.endDate) {
      filteredLogs = filteredLogs.filter(log => log.requestTime <= params.endDate!)
    }
    
    return paginate(filteredLogs, params, ['interfaceName', 'clientIp'])
  },

  // 获取单个日志详情
  async getLog(id: string): Promise<RequestLog | null> {
    await delay()
    const allLogs = [...mockRequestLogs, ...generateMoreLogs(100)]
    return allLogs.find(log => log.id === id) || null
  },

  // 导出日志
  async exportLogs(_params: unknown): Promise<{ success: boolean; downloadUrl?: string; message: string }> {
    await delay(2000) // 导出需要更长时间
    
    // 模拟导出结果
    const success = Math.random() > 0.1 // 90%成功率
    return {
      success,
      downloadUrl: success ? `/api/download/logs-${Date.now()}.csv` : undefined,
      message: success ? '导出成功' : '导出失败：数据量过大'
    }
  }
}

// 请求限制API
export const limitApi = {
  // 获取限制规则列表
  async getLimits(params: PaginationParams = {}): Promise<PaginatedResult<RateLimit>> {
    await delay()
    return paginate(mockRateLimits, params, ['name', 'description', 'type'])
  },

  // 获取单个限制规则
  async getLimit(id: string): Promise<RateLimit | null> {
    await delay()
    return mockRateLimits.find(limit => limit.id === id) || null
  },

  // 创建限制规则
  async createLimit(limitData: Omit<RateLimit, 'id' | 'createdTime' | 'updatedTime' | 'triggeredCount'>): Promise<RateLimit> {
    await delay()
    const newLimit: RateLimit = {
      ...limitData,
      id: `limit-${Date.now()}`,
      createdTime: new Date().toISOString(),
      updatedTime: new Date().toISOString(),
      triggeredCount: 0
    }
    mockRateLimits.push(newLimit)
    return newLimit
  },

  // 更新限制规则
  async updateLimit(id: string, limitData: Partial<RateLimit>): Promise<RateLimit | null> {
    await delay()
    const limitIndex = mockRateLimits.findIndex(limit => limit.id === id)
    if (limitIndex === -1) return null
    
    mockRateLimits[limitIndex] = {
      ...mockRateLimits[limitIndex],
      ...limitData,
      updatedTime: new Date().toISOString()
    }
    return mockRateLimits[limitIndex]
  },

  // 删除限制规则
  async deleteLimit(id: string): Promise<boolean> {
    await delay()
    const limitIndex = mockRateLimits.findIndex(limit => limit.id === id)
    if (limitIndex === -1) return false
    
    mockRateLimits.splice(limitIndex, 1)
    return true
  }
}

// 仪表盘API
export const dashboardApi = {
  // 获取仪表盘统计数据
  async getStats(): Promise<DashboardStats> {
    await delay()
    return updateDashboardStats()
  },

  // 获取实时统计数据（用于自动刷新）
  async getRealTimeStats(): Promise<Pick<DashboardStats, 'totalRequests' | 'successRate' | 'todayRequests' | 'recentRequests'>> {
    await delay(200) // 实时数据延迟更短
    const stats = updateDashboardStats()
    return {
      totalRequests: stats.totalRequests,
      successRate: stats.successRate,
      todayRequests: stats.todayRequests,
      recentRequests: stats.recentRequests
    }
  }
}