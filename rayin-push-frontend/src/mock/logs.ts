import type { RequestLog } from '@/types/data'

export const mockRequestLogs: RequestLog[] = [
  {
    id: '1',
    interfaceId: '1',
    interfaceName: '用户注册通知',
    requestTime: '2024-12-25T10:30:15Z',
    responseTime: '2024-12-25T10:30:16Z',
    duration: 1200,
    status: 'success',
    originalData: {
      user: {
        name: '新用户123',
        email: '<EMAIL>',
        type: 'premium'
      },
      timestamp: '2024-12-25T10:30:15Z'
    },
    processedData: {
      username: '新用户123',
      email: '<EMAIL>',
      registerTime: '2024-12-25T10:30:15Z',
      userType: 'premium'
    },
    channelResults: [
      {
        channelId: '1',
        channelName: '开发团队微信群',
        status: 'success',
        response: 'ok',
        sentAt: '2024-12-25T10:30:15.5Z'
      },
      {
        channelId: '2',
        channelName: '运维告警飞书群',
        status: 'success',
        response: '{"code":0,"msg":"success"}',
        sentAt: '2024-12-25T10:30:15.8Z'
      }
    ],
    clientIp: '*************',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)'
  },
  {
    id: '2',
    interfaceId: '2',
    interfaceName: '订单状态更新',
    requestTime: '2024-12-25T09:45:30Z',
    responseTime: '2024-12-25T09:45:32Z',
    duration: 2100,
    status: 'partial',
    originalData: {
      order: {
        id: 'ORD-20241225-001',
        status: 'paid',
        amount: 299.99
      },
      customer: {
        name: '张三'
      }
    },
    processedData: {
      orderId: 'ORD-20241225-001',
      status: 'paid',
      amount: 299.99,
      customerName: '张三'
    },
    channelResults: [
      {
        channelId: '1',
        channelName: '开发团队微信群',
        status: 'success',
        response: 'ok',
        sentAt: '2024-12-25T09:45:31Z'
      },
      {
        channelId: '3',
        channelName: '业务通知Webhook',
        status: 'failed',
        error: 'Connection timeout',
        sentAt: '2024-12-25T09:45:32Z'
      }
    ],
    clientIp: '*************',
    userAgent: 'PostmanRuntime/7.32.0'
  },
  {
    id: '3',
    interfaceId: '3',
    interfaceName: '系统异常告警',
    requestTime: '2024-12-25T08:20:45Z',
    responseTime: '2024-12-25T08:20:46Z',
    duration: 800,
    status: 'success',
    originalData: {
      alert_level: 'critical',
      alert_message: 'Database connection failed',
      service_name: 'user-service',
      occurred_at: '2024-12-25T08:20:40Z'
    },
    processedData: {
      level: 'critical',
      message: 'Database connection failed',
      service: 'user-service',
      timestamp: '2024-12-25T08:20:40Z'
    },
    channelResults: [
      {
        channelId: '2',
        channelName: '运维告警飞书群',
        status: 'success',
        response: '{"StatusCode":0,"StatusMessage":"success"}',
        sentAt: '2024-12-25T08:20:45.5Z'
      },
      {
        channelId: '4',
        channelName: '测试环境通知',
        status: 'success',
        response: 'Message received',
        sentAt: '2024-12-25T08:20:46Z'
      }
    ],
    clientIp: '*********',
    userAgent: 'AlertManager/1.0'
  },
  {
    id: '4',
    interfaceId: '1',
    interfaceName: '用户注册通知',
    requestTime: '2024-12-24T18:15:20Z',
    responseTime: '2024-12-24T18:15:25Z',
    duration: 5000,
    status: 'failed',
    originalData: {
      user: {
        name: '测试用户',
        email: 'test@invalid-domain',
        type: 'basic'
      },
      timestamp: '2024-12-24T18:15:20Z'
    },
    processedData: {
      username: '测试用户',
      email: 'test@invalid-domain',
      registerTime: '2024-12-24T18:15:20Z',
      userType: 'basic'
    },
    channelResults: [
      {
        channelId: '1',
        channelName: '开发团队微信群',
        status: 'failed',
        error: 'Invalid webhook key',
        sentAt: '2024-12-24T18:15:22Z'
      },
      {
        channelId: '2',
        channelName: '运维告警飞书群',
        status: 'failed',
        error: 'Rate limit exceeded',
        sentAt: '2024-12-24T18:15:25Z'
      }
    ],
    errorMessage: 'All notification channels failed',
    clientIp: '*************',
    userAgent: 'curl/7.68.0'
  },
  {
    id: '5',
    interfaceId: '5',
    interfaceName: '日志分析结果',
    requestTime: '2024-12-24T06:00:00Z',
    responseTime: '2024-12-24T06:00:01Z',
    duration: 600,
    status: 'success',
    originalData: {
      rawLog: 'Date: 2024-12-24\nErrors: 15\nWarnings: 42\nSummary: System performance degraded'
    },
    processedData: {
      date: '2024-12-24',
      errorCount: '15',
      warningCount: '42',
      summary: 'System performance degraded'
    },
    channelResults: [
      {
        channelId: '2',
        channelName: '运维告警飞书群',
        status: 'success',
        response: '{"code":0,"msg":"success"}',
        sentAt: '2024-12-24T06:00:00.8Z'
      }
    ],
    clientIp: '*********',
    userAgent: 'LogAnalyzer/2.1'
  }
]

// 生成更多历史日志数据用于分页和趋势分析
export const generateMoreLogs = (count: number): RequestLog[] => {
  const logs: RequestLog[] = []
  const interfaces = ['1', '2', '3', '4', '5']
  const interfaceNames = ['用户注册通知', '订单状态更新', '系统异常告警', '支付成功通知', '日志分析结果']
  const statuses: RequestLog['status'][] = ['success', 'failed', 'processing', 'partial']
  const ips = ['*************', '*************', '*************', '*********', '*********']

  for (let i = 0; i < count; i++) {
    const interfaceIndex = Math.floor(Math.random() * interfaces.length)
    const status = statuses[Math.floor(Math.random() * statuses.length)]
    const requestTime = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000) // 最近7天
    const responseTime = new Date(requestTime.getTime() + Math.random() * 5000) // 0-5秒延迟

    logs.push({
      id: `generated-${i + 1}`,
      interfaceId: interfaces[interfaceIndex],
      interfaceName: interfaceNames[interfaceIndex],
      requestTime: requestTime.toISOString(),
      responseTime: responseTime.toISOString(),
      duration: responseTime.getTime() - requestTime.getTime(),
      status,
      originalData: { sample: 'data' },
      processedData: { processed: 'data' },
      channelResults: [
        {
          channelId: '1',
          channelName: '开发团队微信群',
          status: status === 'failed' ? 'failed' : 'success',
          response: status === 'failed' ? undefined : 'ok',
          error: status === 'failed' ? 'Sample error' : undefined,
          sentAt: requestTime.toISOString()
        }
      ],
      clientIp: ips[Math.floor(Math.random() * ips.length)],
      userAgent: 'Generated/1.0'
    })
  }

  return logs
}