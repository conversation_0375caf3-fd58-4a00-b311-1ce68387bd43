'use client'

import { useEffect, useState } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { dashboardApi, userApi, configApi, channelApi, logApi, limitApi } from '@/mock/api'
import type { DashboardStats } from '@/types/data'

export function MockDataTest() {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(false)

  const loadStats = async () => {
    setLoading(true)
    try {
      const data = await dashboardApi.getStats()
      setStats(data)
    } catch (error) {
      console.error('Failed to load stats:', error)
    } finally {
      setLoading(false)
    }
  }

  const testApis = async () => {
    console.log('Testing mock APIs...')
    
    try {
      // 测试用户API
      const users = await userApi.getUsers({ page: 1, pageSize: 5 })
      console.log('Users:', users)

      // 测试配置API
      const configs = await configApi.getConfigs({ page: 1, pageSize: 3 })
      console.log('Configs:', configs)

      // 测试渠道API
      const channels = await channelApi.getChannels()
      console.log('Channels:', channels)

      // 测试日志API
      const logs = await logApi.getLogs({ page: 1, pageSize: 5 })
      console.log('Logs:', logs)

      // 测试限制API
      const limits = await limitApi.getLimits()
      console.log('Limits:', limits)

      alert('所有API测试完成，请查看控制台输出')
    } catch (error) {
      console.error('API test failed:', error)
    }
  }

  useEffect(() => {
    loadStats()
  }, [])

  if (loading || !stats) {
    return <div>Loading mock data...</div>
  }

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">模拟数据系统测试</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-6">
          <h3 className="font-semibold text-sm text-muted-foreground">总用户数</h3>
          <p className="text-2xl font-bold">{stats.totalUsers}</p>
        </Card>
        
        <Card className="p-6">
          <h3 className="font-semibold text-sm text-muted-foreground">总请求数</h3>
          <p className="text-2xl font-bold">{stats.totalRequests.toLocaleString()}</p>
        </Card>
        
        <Card className="p-6">
          <h3 className="font-semibold text-sm text-muted-foreground">成功率</h3>
          <p className="text-2xl font-bold">{stats.successRate}%</p>
        </Card>
        
        <Card className="p-6">
          <h3 className="font-semibold text-sm text-muted-foreground">今日请求</h3>
          <p className="text-2xl font-bold">{stats.todayRequests}</p>
        </Card>
      </div>

      <div className="flex gap-4">
        <Button onClick={loadStats}>刷新统计数据</Button>
        <Button onClick={testApis} variant="outline">测试所有API</Button>
      </div>

      <Card className="p-6">
        <h3 className="font-semibold mb-4">最近请求</h3>
        <div className="space-y-2">
          {stats.recentRequests.slice(0, 5).map((log) => (
            <div key={log.id} className="flex justify-between items-center p-2 bg-muted rounded">
              <span className="text-sm">{log.interfaceName}</span>
              <span className={`text-xs px-2 py-1 rounded ${
                log.status === 'success' ? 'bg-green-100 text-green-800' :
                log.status === 'failed' ? 'bg-red-100 text-red-800' :
                'bg-yellow-100 text-yellow-800'
              }`}>
                {log.status}
              </span>
            </div>
          ))}
        </div>
      </Card>
    </div>
  )
}