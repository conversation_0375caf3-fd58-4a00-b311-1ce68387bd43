'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { LucideIcon } from 'lucide-react'
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@/components/ui/sidebar'

interface NavItem {
  title: string
  url: string
  icon?: LucideIcon
  isActive?: boolean
  items?: NavItem[]
}

interface NavGroupProps {
  title?: string
  items: NavItem[]
}

export function NavGroup({ title, items }: NavGroupProps) {
  const pathname = usePathname()
  const { state, isMobile, setOpenMobile } = useSidebar()
  
  // 获取当前语言代码
  const getCurrentLocale = () => {
    const localeMatch = pathname.match(/^\/([a-z]{2})/)
    return localeMatch ? localeMatch[1] : 'zh' // 默认为中文
  }
  
  const currentLocale = getCurrentLocale()
  
  // 检查路径是否匹配当前路由
  const isActiveRoute = (url: string) => {
    // 移除语言前缀来比较路径
    const currentPath = pathname.replace(/^\/[a-z]{2}/, '') || '/'
    const targetPath = url === '/' ? '/' : url
    return currentPath === targetPath || currentPath.startsWith(targetPath + '/')
  }
  
  // 为URL添加语言前缀
  const getLocalizedUrl = (url: string) => {
    return `/${currentLocale}${url}`
  }

  // 处理导航点击
  const handleNavClick = (e: React.MouseEvent) => {
    // 在移动端点击导航项后关闭侧边栏
    if (isMobile) {
      setOpenMobile(false)
    }
    // 阻止事件冒泡，防止触发侧边栏切换
    e.stopPropagation()
  }

  return (
    <SidebarGroup>
      {title && <SidebarGroupLabel>{title}</SidebarGroupLabel>}
      <SidebarGroupContent>
        <SidebarMenu>
          {items.map((item, index) => (
            <SidebarMenuItem key={`${item.url}-${index}`}>
              <SidebarMenuButton 
                asChild 
                isActive={isActiveRoute(item.url)}
                tooltip={state === 'collapsed' ? item.title : undefined}
              >
                <Link 
                  href={getLocalizedUrl(item.url)}
                  onClick={handleNavClick}
                >
                  {item.icon && <item.icon />}
                  <span>{item.title}</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}