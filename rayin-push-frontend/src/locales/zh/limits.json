{"title": "请求限制", "rateLimits": "速率限制", "ipLimits": "IP限制", "createRule": "创建规则", "editRule": "编辑规则", "deleteRule": "删除规则", "ruleName": "规则名称", "ruleDescription": "规则描述", "timeWindow": "时间窗口", "requestLimit": "请求限制", "ruleStatus": "规则状态", "ruleType": "规则类型", "globalLimit": "全局限制", "perIpLimit": "单IP限制", "perUserLimit": "单用户限制", "seconds": "秒", "minutes": "分钟", "hours": "小时", "days": "天", "requestsPerTimeWindow": "每时间窗口请求数", "ipAddress": "IP地址", "ipRange": "IP段", "singleIp": "单个IP", "ipWhitelist": "IP白名单", "ipBlacklist": "IP黑名单", "addIpRule": "添加IP规则", "removeIpRule": "移除IP规则", "currentLimits": "当前限制", "activeLimits": "生效中的限制", "limitStatistics": "限制统计", "triggeredCount": "触发次数", "lastTriggered": "最后触发时间", "affectedRequests": "受影响请求", "exemptIps": "豁免IP", "exemptUsers": "豁免用户", "enableRule": "启用规则", "disableRule": "禁用规则", "ruleHistory": "规则历史", "limitExceeded": "超出限制", "rateLimitReset": "限制重置时间"}