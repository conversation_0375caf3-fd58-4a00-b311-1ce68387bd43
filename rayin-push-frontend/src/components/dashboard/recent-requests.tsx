'use client'

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { RequestLog } from '@/types/data'
import { useTypedTranslation } from '@/hooks/use-typed-translation'
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertTriangle, 
  ExternalLink,
  Eye
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { useRouter } from 'next/navigation'

interface RecentRequestsProps {
  requests: RequestLog[]
  loading?: boolean
  className?: string
}

const getStatusIcon = (status: RequestLog['status']) => {
  switch (status) {
    case 'success':
      return <CheckCircle className="h-4 w-4 text-green-500 dark:text-green-400" />
    case 'failed':
      return <XCircle className="h-4 w-4 text-red-500 dark:text-red-400" />
    case 'processing':
      return <Clock className="h-4 w-4 text-blue-500 dark:text-blue-400" />
    case 'partial':
      return <AlertTriangle className="h-4 w-4 text-yellow-500 dark:text-yellow-400" />
    default:
      return <Clock className="h-4 w-4 text-gray-500 dark:text-gray-400" />
  }
}

const getStatusColor = (status: RequestLog['status']) => {
  switch (status) {
    case 'success':
      return 'text-green-600 bg-green-50 border-green-200 dark:text-green-300 dark:bg-green-900/20 dark:border-green-800'
    case 'failed':
      return 'text-red-600 bg-red-50 border-red-200 dark:text-red-300 dark:bg-red-900/20 dark:border-red-800'
    case 'processing':
      return 'text-blue-600 bg-blue-50 border-blue-200 dark:text-blue-300 dark:bg-blue-900/20 dark:border-blue-800'
    case 'partial':
      return 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:text-yellow-300 dark:bg-yellow-900/20 dark:border-yellow-800'
    default:
      return 'text-gray-600 bg-gray-50 border-gray-200 dark:text-gray-300 dark:bg-gray-800 dark:border-gray-600'
  }
}

const formatTime = (timeString: string) => {
  const date = new Date(timeString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (diff < 60000) { // 小于1分钟
    return '刚刚'
  } else if (diff < 3600000) { // 小于1小时
    const minutes = Math.floor(diff / 60000)
    return `${minutes}分钟前`
  } else if (diff < 86400000) { // 小于1天
    const hours = Math.floor(diff / 3600000)
    return `${hours}小时前`
  } else {
    return date.toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
}

const formatDuration = (duration: number) => {
  if (duration < 1000) {
    return `${duration}ms`
  } else {
    return `${(duration / 1000).toFixed(1)}s`
  }
}

export function RecentRequests({ requests, loading, className }: RecentRequestsProps) {
  const { t } = useTypedTranslation('dashboard')
  const router = useRouter()

  const getStatusText = (status: RequestLog['status']) => {
    switch (status) {
      case 'success':
        return t('success')
      case 'failed':
        return t('failed')
      case 'processing':
        return t('processing')
      case 'partial':
        return t('partial')
      default:
        return t('pending')
    }
  }

  const handleViewDetails = (requestId: string) => {
    // 导航到日志详情页面
    router.push(`/logs/${requestId}`)
  }

  const handleViewAllLogs = () => {
    router.push('/logs')
  }

  if (loading) {
    return (
      <Card className={cn('', className)}>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>{t('recentRequests')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="flex items-center space-x-4 animate-pulse">
              <div className="w-4 h-4 bg-gray-200 rounded-full"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
              <div className="w-16 h-6 bg-gray-200 rounded"></div>
            </div>
          ))}
        </CardContent>
      </Card>
    )
  }

  if (!requests || requests.length === 0) {
    return (
      <Card className={cn('', className)}>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>{t('recentRequests')}</span>
            <Button variant="outline" size="sm" onClick={handleViewAllLogs}>
              <ExternalLink className="h-4 w-4 mr-1" />
              {t('viewAllLogs')}
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>{t('noRecentRequests')}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // 只显示最近6条记录
  const recentRequests = requests.slice(0, 6)

  return (
    <Card className={cn('', className)}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>{t('recentRequests')}</span>
          <Button variant="outline" size="sm" onClick={handleViewAllLogs}>
            <ExternalLink className="h-4 w-4 mr-1" />
            {t('viewAllLogs')}
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {recentRequests.map((request) => (
          <div 
            key={request.id}
            className="flex items-center space-x-4 p-3 rounded-lg border hover:bg-muted/50 transition-colors cursor-pointer group"
            onClick={() => handleViewDetails(request.id)}
          >
            {/* 状态图标 */}
            <div className="flex-shrink-0">
              {getStatusIcon(request.status)}
            </div>

            {/* 请求信息 */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-1">
                <h4 className="font-medium text-sm truncate">
                  {request.interfaceName}
                </h4>
                <Badge 
                  variant="outline" 
                  className={cn('text-xs', getStatusColor(request.status))}
                >
                  {getStatusText(request.status)}
                </Badge>
              </div>
              <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                <span>{formatTime(request.requestTime)}</span>
                <span>{t('duration')} {formatDuration(request.duration)}</span>
                <span>{t('from')} {request.clientIp}</span>
              </div>
            </div>

            {/* 渠道结果概览 */}
            <div className="flex-shrink-0 hidden sm:flex items-center space-x-1">
              <span className="text-xs text-muted-foreground">
                {request.channelResults.filter(r => r.status === 'success').length}/
                {request.channelResults.length} {t('channels')}
              </span>
            </div>

            {/* 查看详情按钮 */}
            <div className="flex-shrink-0">
              <Button 
                variant="ghost" 
                size="sm"
                className="opacity-0 group-hover:opacity-100 transition-opacity"
                title={t('viewDetails')}
                onClick={(e) => {
                  e.stopPropagation()
                  handleViewDetails(request.id)
                }}
              >
                <Eye className="h-4 w-4" />
              </Button>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  )
}