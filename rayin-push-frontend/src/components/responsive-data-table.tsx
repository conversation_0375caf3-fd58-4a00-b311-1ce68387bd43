'use client'

import React, { useState, useMemo, useEffect } from 'react'
import {
  ChevronUp,
  ChevronDown,
  ChevronsUpDown,
  RotateCcw,
  Loader2,
  Monitor,
  Smartphone
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { MobileDataCard } from '@/components/mobile-data-card'
import { cn } from '@/lib/utils'
import { useTypedTranslation } from '@/hooks/use-typed-translation'
import type {
  DataTableProps,
  DataTableColumn,
  SortingState,
  FilteringState,
  PaginationState
} from '@/types/dataTable'

export function ResponsiveDataTable<T extends Record<string, unknown>>({
  columns,
  data,
  loading = { isLoading: false },
  config = {},
  actions = {},
  className,
  emptyMessage,
  pageCount,
  total
}: DataTableProps<T>) {
  const { t } = useTypedTranslation('common')
  
  // 默认配置
  const defaultConfig = {
    enableSorting: true,
    enableFiltering: true,
    enablePagination: true,
    enableRowSelection: false,
    showToolbar: true,
    showFooter: true,
    pageSize: 10,
    pageSizeOptions: [10, 20, 50, 100],
    ...config
  }

  // 响应式状态
  const [isMobile, setIsMobile] = useState(false)
  const [viewMode, setViewMode] = useState<'auto' | 'desktop' | 'mobile'>('auto')

  // 状态管理
  const [sorting, setSorting] = useState<SortingState[]>([])
  const [filtering, setFiltering] = useState<FilteringState[]>([])
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: defaultConfig.pageSize
  })
  const [globalFilter, setGlobalFilter] = useState('')

  // 检测屏幕尺寸
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    checkIsMobile()
    window.addEventListener('resize', checkIsMobile)
    return () => window.removeEventListener('resize', checkIsMobile)
  }, [])

  // 决定显示模式
  const shouldShowMobile = viewMode === 'mobile' || (viewMode === 'auto' && isMobile)

  // 处理排序
  const handleSortingChange = (columnId: string) => {
    if (!defaultConfig.enableSorting) return

    const existingSort = sorting.find(s => s.id === columnId)
    let newSorting: SortingState[]

    if (!existingSort) {
      newSorting = [{ id: columnId, desc: false }]
    } else if (!existingSort.desc) {
      newSorting = [{ id: columnId, desc: true }]
    } else {
      newSorting = []
    }

    setSorting(newSorting)
    actions.onSortingChange?.(newSorting)
  }

  // 获取排序图标
  const getSortIcon = (columnId: string) => {
    const sort = sorting.find(s => s.id === columnId)
    if (!sort) return <ChevronsUpDown className="h-4 w-4" />
    return sort.desc ? 
      <ChevronDown className="h-4 w-4" /> : 
      <ChevronUp className="h-4 w-4" />
  }

  // 处理全局搜索
  const filteredData = useMemo(() => {
    if (!globalFilter) return data
    
    return data.filter((row) =>
      Object.values(row).some((value) =>
        String(value).toLowerCase().includes(globalFilter.toLowerCase())
      )
    )
  }, [data, globalFilter])

  // 处理分页
  const paginatedData = useMemo(() => {
    if (!defaultConfig.enablePagination) return filteredData
    
    const startIndex = pagination.pageIndex * pagination.pageSize
    const endIndex = startIndex + pagination.pageSize
    return filteredData.slice(startIndex, endIndex)
  }, [filteredData, pagination, defaultConfig.enablePagination])

  // 分页信息
  const totalPages = Math.ceil(filteredData.length / pagination.pageSize)
  const displayedTotal = total || filteredData.length

  // 渲染单元格内容
  const renderCell = (column: DataTableColumn<T>, row: T) => {
    if (column.cell) {
      return column.cell(row)
    }
    
    if (column.accessorKey) {
      const value = row[column.accessorKey]
      return value !== null && value !== undefined ? String(value) : '-'
    }
    
    return '-'
  }

  // 空状态组件
  const EmptyState = () => (
    <div className="text-center py-8 text-muted-foreground">
      <div className="mb-2">📊</div>
      <p className="text-sm">{emptyMessage || t('noData')}</p>
    </div>
  )

  // 加载状态组件
  const LoadingState = () => (
    <div className="text-center py-8">
      <div className="flex items-center justify-center space-x-2">
        <Loader2 className="h-5 w-5 animate-spin" />
        <span className="text-sm text-muted-foreground">{t('loading')}</span>
      </div>
    </div>
  )

  return (
    <div className={cn("space-y-4", className)}>
      {/* 工具栏 */}
      {defaultConfig.showToolbar && (
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div className="flex items-center space-x-2 w-full sm:w-auto">
            {/* 全局搜索 */}
            {defaultConfig.enableFiltering && (
              <Input
                placeholder={`${t('search')}...`}
                value={globalFilter}
                onChange={(e) => setGlobalFilter(e.target.value)}
                className="w-full sm:w-64"
              />
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {/* 视图切换按钮 */}
            <div className="hidden sm:flex items-center space-x-1 border rounded-md p-1">
              <Button
                variant={viewMode === 'desktop' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('desktop')}
                className="h-7 px-2"
              >
                <Monitor className="h-3 w-3" />
              </Button>
              <Button
                variant={viewMode === 'auto' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('auto')}
                className="h-7 px-2"
              >
                {t('auto')}
              </Button>
              <Button
                variant={viewMode === 'mobile' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('mobile')}
                className="h-7 px-2"
              >
                <Smartphone className="h-3 w-3" />
              </Button>
            </div>

            {/* 刷新按钮 */}
            <Button
              variant="outline"
              size="sm"
              onClick={actions.onRefresh}
              disabled={loading.isLoading}
            >
              {loading.isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <RotateCcw className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      )}

      {/* 内容区域 */}
      <div className="min-h-[200px]">
        {loading.isLoading ? (
          <LoadingState />
        ) : paginatedData.length === 0 ? (
          <EmptyState />
        ) : shouldShowMobile ? (
          /* 移动端卡片视图 */
          <div className="space-y-3">
            {paginatedData.map((row, index) => (
              <MobileDataCard
                key={index}
                columns={columns}
                row={row}
                index={pagination.pageIndex * pagination.pageSize + index}
              />
            ))}
          </div>
        ) : (
          /* 桌面端表格视图 */
          <div className="rounded-md border overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  {columns.map((column) => (
                    <TableHead
                      key={column.id}
                      className={cn(
                        "text-left whitespace-nowrap",
                        column.align === 'center' && "text-center",
                        column.align === 'right' && "text-right",
                        column.sortable && defaultConfig.enableSorting && "cursor-pointer select-none hover:bg-muted/50",
                        column.headerClassName
                      )}
                      style={{
                        width: column.width,
                        minWidth: column.minWidth || '100px',
                        maxWidth: column.maxWidth
                      }}
                      onClick={() => column.sortable && handleSortingChange(column.id)}
                    >
                      <div className="flex items-center space-x-2">
                        <span>{column.header}</span>
                        {column.sortable && defaultConfig.enableSorting && (
                          <span className="text-muted-foreground">
                            {getSortIcon(column.id)}
                          </span>
                        )}
                      </div>
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              
              <TableBody>
                {paginatedData.map((row, index) => (
                  <TableRow key={index} className="hover:bg-muted/50">
                    {columns.map((column) => (
                      <TableCell
                        key={column.id}
                        className={cn(
                          "whitespace-nowrap",
                          column.align === 'center' && "text-center",
                          column.align === 'right' && "text-right",
                          column.className
                        )}
                      >
                        {renderCell(column, row)}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </div>

      {/* 错误状态 */}
      {loading.error && (
        <div className="text-center py-4 text-destructive">
          <p className="text-sm">{loading.error}</p>
        </div>
      )}

      {/* 分页器 */}
      {defaultConfig.enablePagination && defaultConfig.showFooter && paginatedData.length > 0 && (
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div className="text-sm text-muted-foreground">
            {t('showing', { 
              from: pagination.pageIndex * pagination.pageSize + 1, 
              to: Math.min((pagination.pageIndex + 1) * pagination.pageSize, displayedTotal), 
              total: displayedTotal 
            })}
          </div>
          
          <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-6">
            <div className="flex items-center space-x-2">
              <p className="text-sm font-medium whitespace-nowrap">{t('rowsPerPage')}</p>
              <select
                value={pagination.pageSize}
                onChange={(e) => {
                  const newPageSize = Number(e.target.value)
                  const newPagination = {
                    pageIndex: 0,
                    pageSize: newPageSize
                  }
                  setPagination(newPagination)
                  actions.onPaginationChange?.(newPagination)
                }}
                className="h-8 w-[70px] rounded border border-input bg-background px-3 py-1 text-sm"
              >
                {defaultConfig.pageSizeOptions.map((size) => (
                  <option key={size} value={size}>
                    {size}
                  </option>
                ))}
              </select>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const newPagination = { ...pagination, pageIndex: 0 }
                  setPagination(newPagination)
                  actions.onPaginationChange?.(newPagination)
                }}
                disabled={pagination.pageIndex === 0}
                className="hidden sm:inline-flex"
              >
                {t('firstPage')}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const newPagination = { 
                    ...pagination, 
                    pageIndex: pagination.pageIndex - 1 
                  }
                  setPagination(newPagination)
                  actions.onPaginationChange?.(newPagination)
                }}
                disabled={pagination.pageIndex === 0}
              >
                {t('previousPage')}
              </Button>
              
              <div className="text-sm font-medium whitespace-nowrap">
                {t('page', { current: pagination.pageIndex + 1, total: totalPages })}
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const newPagination = { 
                    ...pagination, 
                    pageIndex: pagination.pageIndex + 1 
                  }
                  setPagination(newPagination)
                  actions.onPaginationChange?.(newPagination)
                }}
                disabled={pagination.pageIndex >= totalPages - 1}
              >
                {t('nextPage')}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const newPagination = { 
                    ...pagination, 
                    pageIndex: totalPages - 1 
                  }
                  setPagination(newPagination)
                  actions.onPaginationChange?.(newPagination)
                }}
                disabled={pagination.pageIndex >= totalPages - 1}
                className="hidden sm:inline-flex"
              >
                {t('lastPage')}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}