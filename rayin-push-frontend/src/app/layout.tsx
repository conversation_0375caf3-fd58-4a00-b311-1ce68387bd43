import type { Metadata } from 'next'
import { Geist, <PERSON>eist_Mono } from 'next/font/google'
import './globals.css'
import { AppProviders } from '@/components/providers'

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
})

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
})

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
}

export const metadata: Metadata = {
  title: {
    template: '%s | Rayin Push',
    default: '<PERSON><PERSON> Push - 消息推送通知系统',
  },
  description: '强大的消息推送通知系统，支持多渠道消息推送和智能路由',
  keywords: ['推送', '通知', '消息', 'webhook', '微信', '飞书'],
  authors: [{ name: '<PERSON><PERSON>ush <PERSON>' }],
  creator: '<PERSON><PERSON>',
  publisher: '<PERSON><PERSON>ush',
  robots: {
    index: false,
    follow: false,
  },
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="zh-CN" className={`${geistSans.variable} ${geistMono.variable}`} suppressHydrationWarning>
      <body className="antialiased min-h-screen bg-background font-sans" suppressHydrationWarning>
        <AppProviders>
          {children}
        </AppProviders>
      </body>
    </html>
  )
}
