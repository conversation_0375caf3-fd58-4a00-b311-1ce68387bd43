'use client'

import { useTypedTranslation } from '@/hooks/use-typed-translation'
import { cn } from '@/lib/utils'

interface DashboardToolbarProps {
  className?: string
}

export function DashboardToolbar({
  className
}: DashboardToolbarProps) {
  const { t } = useTypedTranslation('dashboard')

  return (
    <div className={cn('flex items-center justify-between gap-4', className)}>
      <div className="flex items-center gap-2">
        <h2 className="text-lg font-semibold">{t('statistics')}</h2>
      </div>
    </div>
  )
}