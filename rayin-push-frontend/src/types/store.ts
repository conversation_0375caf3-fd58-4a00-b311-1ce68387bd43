import type { SupportedLocale } from './i18n'

// 主题类型
export type Theme = 'light' | 'dark' | 'system'

// 侧边栏状态类型
export interface SidebarState {
  isOpen: boolean
  isCollapsed: boolean
  isMobile: boolean
}

// 全局状态接口
export interface GlobalState {
  // 主题设置
  theme: Theme
  setTheme: (theme: Theme) => void
  
  // 语言设置
  locale: SupportedLocale
  setLocale: (locale: SupportedLocale) => void
  
  // 侧边栏状态
  sidebar: SidebarState
  setSidebarOpen: (isOpen: boolean) => void
  setSidebarCollapsed: (isCollapsed: boolean) => void
  setSidebarMobile: (isMobile: boolean) => void
  toggleSidebar: () => void
  toggleSidebarCollapse: () => void
}

// 用户状态接口
export interface User {
  id: string
  username: string
  email: string
  role: 'admin' | 'user'
  avatar?: string
  createdTime: string
  lastLoginAt?: string
}

export interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  login: (credentials: { username: string; password: string }) => Promise<void>
  logout: () => void
  updateUser: (user: Partial<User>) => void
}

// 应用状态接口
export interface AppState {
  isInitialized: boolean
  isLoading: boolean
  error: string | null
  setInitialized: (initialized: boolean) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  clearError: () => void
}

// 持久化配置类型
export interface PersistConfig {
  name: string
  version: number
  storage: {
    getItem: (name: string) => string | null | Promise<string | null>
    setItem: (name: string, value: string) => void | Promise<void>
    removeItem: (name: string) => void | Promise<void>
  }
  partialize?: (state: unknown) => unknown
  onRehydrateStorage?: () => (state?: unknown, error?: Error) => void
}