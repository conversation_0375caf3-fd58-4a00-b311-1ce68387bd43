import {
  LayoutDashboard,
  Settings,
  Bell,
  FileText,
  Shield,
  Users,
  BarChart3,
  Webhook,
  Database,
  Activity
} from 'lucide-react'
import type { NavigationSection, NavigationItem } from '@/types/navigation'

export const navigationConfig: NavigationSection[] = [
  {
    id: 'main',
    items: [
      {
        id: 'dashboard',
        title: '仪表盘',
        titleKey: 'dashboard',
        icon: LayoutDashboard,
        href: '/dashboard',
      },
    ]
  },
  {
    id: 'management',
    title: '管理',
    titleKey: 'management',
    items: [
      {
        id: 'configs',
        title: '接口配置',
        titleKey: 'config',
        icon: Settings,
        href: '/config',
      },
      {
        id: 'channels',
        title: '通知渠道',
        titleKey: 'channels',
        icon: Bell,
        href: '/channels',
      },
      {
        id: 'logs',
        title: '请求日志',
        titleKey: 'logs',
        icon: FileText,
        href: '/logs',
      },
      {
        id: 'limits',
        title: '请求限制',
        titleKey: 'limits',
        icon: Shield,
        href: '/limits',
      },
    ]
  },
  {
    id: 'system',
    title: '系统',
    titleKey: 'system',
    items: [
      {
        id: 'users',
        title: '用户管理',
        titleKey: 'users',
        icon: Users,
        href: '/users',
        requireAuth: true,
        roles: ['admin'],
      },
      {
        id: 'monitoring',
        title: '系统监控',
        titleKey: 'monitoring',
        icon: Activity,
        href: '/monitoring',
        requireAuth: true,
        roles: ['admin'],
        children: [
          {
            id: 'analytics',
            title: '数据分析',
            titleKey: 'analytics',
            icon: BarChart3,
            href: '/monitoring/analytics',
          },
          {
            id: 'webhooks',
            title: 'Webhook管理',
            titleKey: 'webhooks',
            icon: Webhook,
            href: '/monitoring/webhooks',
          },
          {
            id: 'database',
            title: '数据库状态',
            titleKey: 'database',
            icon: Database,
            href: '/monitoring/database',
          },
        ]
      },
    ]
  }
]

// 获取扁平化的导航项列表（用于搜索等功能）
export const getFlatNavigationItems = () => {
  const items: NavigationItem[] = []
  
  const flatten = (navItems: NavigationItem[]) => {
    navItems.forEach(item => {
      items.push(item)
      if (item.children) {
        flatten(item.children)
      }
    })
  }

  navigationConfig.forEach(section => {
    flatten(section.items)
  })

  return items
}

// 根据用户角色过滤导航项
export const filterNavigationByRole = (
  navigation: NavigationSection[],
  userRole?: 'admin' | 'user',
  isAuthenticated = false
): NavigationSection[] => {
  const filterItems = (items: NavigationItem[]): NavigationItem[] => {
    return items.filter(item => {
      // 检查认证要求
      if (item.requireAuth && !isAuthenticated) {
        return false
      }

      // 检查角色要求
      if (item.roles && userRole && !item.roles.includes(userRole)) {
        return false
      }

      // 递归过滤子项
      if (item.children) {
        item.children = filterItems(item.children)
        // 如果所有子项都被过滤掉，则隐藏父项
        if (item.children.length === 0) {
          return false
        }
      }

      return true
    })
  }

  return navigation.map(section => ({
    ...section,
    items: filterItems(section.items)
  })).filter(section => section.items.length > 0)
}