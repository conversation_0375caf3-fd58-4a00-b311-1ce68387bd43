'use client'

import React, { useState } from 'react'
import { AppLayout } from '@/components/app-layout'
import { FilterToolbar } from '@/components/filter-toolbar'
import { ResponsiveDataTable } from '@/components/responsive-data-table'
import { Badge } from '@/components/ui/badge'
import { Card } from '@/components/ui/card'
import { useTypedTranslation } from '@/hooks/use-typed-translation'
import { useStoreInitialization } from '@/hooks/use-store'
import type { DataTableColumn } from '@/types/dataTable'
import type { FilterState, FilterOption } from '@/types/filters'

// 示例数据类型
interface LogData extends Record<string, unknown> {
  id: number
  requestId: string
  interface: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE'
  status: 'success' | 'failed' | 'pending'
  responseTime: number
  createdTime: string
  userAgent: string
  ip: string
}

export default function FilterTestPage() {
  const { isLoading: translationLoading } = useTypedTranslation('common')
  const { isHydrated } = useStoreInitialization()
  const [isLoading, setIsLoading] = useState(false)

  // 原始数据
  const originalData: LogData[] = [
    {
      id: 1,
      requestId: 'req_001',
      interface: '/api/users',
      method: 'GET',
      status: 'success',
      responseTime: 125,
      createdTime: '2024-01-15T10:30:00Z',
      userAgent: 'Mozilla/5.0',
      ip: '***********'
    },
    {
      id: 2,
      requestId: 'req_002',
      interface: '/api/orders',
      method: 'POST',
      status: 'failed',
      responseTime: 580,
      createdTime: '2024-01-15T11:45:00Z',
      userAgent: 'Chrome/91.0',
      ip: '***********'
    },
    {
      id: 3,
      requestId: 'req_003',
      interface: '/api/products',
      method: 'GET',
      status: 'success',
      responseTime: 95,
      createdTime: '2024-01-16T09:15:00Z',
      userAgent: 'Safari/14.0',
      ip: '***********'
    },
    {
      id: 4,
      requestId: 'req_004',
      interface: '/api/auth/login',
      method: 'POST',
      status: 'pending',
      responseTime: 1200,
      createdTime: '2024-01-16T14:20:00Z',
      userAgent: 'Firefox/88.0',
      ip: '***********'
    },
    {
      id: 5,
      requestId: 'req_005',
      interface: '/api/users',
      method: 'PUT',
      status: 'success',
      responseTime: 200,
      createdTime: '2024-01-17T08:30:00Z',
      userAgent: 'Edge/91.0',
      ip: '***********'
    },
    {
      id: 6,
      requestId: 'req_006',
      interface: '/api/orders',
      method: 'DELETE',
      status: 'failed',
      responseTime: 450,
      createdTime: '2024-01-17T16:45:00Z',
      userAgent: 'Chrome/92.0',
      ip: '***********'
    }
  ]

  // 筛选状态
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    dateRange: { start: null, end: null },
    status: undefined
  })

  // 状态筛选选项
  const statusOptions: FilterOption<string>[] = [
    { label: '成功', value: 'success', count: 3 },
    { label: '失败', value: 'failed', count: 2 },
    { label: '处理中', value: 'pending', count: 1 }
  ]

  // 表格列定义
  const columns: DataTableColumn<LogData>[] = [
    {
      id: 'requestId',
      header: '请求ID',
      accessorKey: 'requestId',
      sortable: true,
      width: '120px'
    },
    {
      id: 'interface',
      header: '接口',
      accessorKey: 'interface',
      sortable: true,
      width: '150px'
    },
    {
      id: 'method',
      header: '方法',
      cell: (row) => (
        <Badge variant="outline" className="text-xs">
          {row.method}
        </Badge>
      ),
      width: '80px'
    },
    {
      id: 'status',
      header: '状态',
      cell: (row) => {
        const statusConfig = {
          success: { label: '成功', variant: 'default' as const },
          failed: { label: '失败', variant: 'destructive' as const },
          pending: { label: '处理中', variant: 'secondary' as const }
        }
        const config = statusConfig[row.status]
        return <Badge variant={config.variant}>{config.label}</Badge>
      },
      width: '100px'
    },
    {
      id: 'responseTime',
      header: '响应时间',
      cell: (row) => `${row.responseTime}ms`,
      sortable: true,
      align: 'right' as const,
      width: '100px'
    },
    {
      id: 'createdTime',
      header: '创建时间',
      cell: (row) => new Date(row.createdTime).toLocaleString('zh-CN'),
      sortable: true,
      width: '160px'
    },
    {
      id: 'ip',
      header: 'IP地址',
      accessorKey: 'ip',
      width: '120px'
    }
  ]

  // 筛选数据
  const getFilteredData = (): LogData[] => {
    let filtered = [...originalData]

    // 搜索筛选
    if (filters.search) {
      const searchLower = filters.search.toLowerCase()
      filtered = filtered.filter(item =>
        item.requestId.toLowerCase().includes(searchLower) ||
        item.interface.toLowerCase().includes(searchLower) ||
        item.method.toLowerCase().includes(searchLower) ||
        item.ip.includes(searchLower)
      )
    }

    // 状态筛选
    if (filters.status) {
      if (Array.isArray(filters.status)) {
        filtered = filtered.filter(item => (filters.status as string[]).includes(item.status))
      } else {
        filtered = filtered.filter(item => item.status === filters.status)
      }
    }

    // 日期范围筛选
    if (filters.dateRange.start || filters.dateRange.end) {
      filtered = filtered.filter(item => {
        const itemDate = new Date(item.createdTime)
        if (filters.dateRange.start && itemDate < filters.dateRange.start) return false
        if (filters.dateRange.end && itemDate > filters.dateRange.end) return false
        return true
      })
    }

    return filtered
  }

  const filteredData = getFilteredData()

  // 模拟刷新
  const handleRefresh = () => {
    setIsLoading(true)
    setTimeout(() => {
      setIsLoading(false)
    }, 1000)
  }

  if (translationLoading || !isHydrated) {
    return (
      <AppLayout title="筛选组件测试">
        <div className="flex items-center justify-center h-full">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </AppLayout>
    )
  }

  return (
    <AppLayout title="筛选组件测试">
      <div className="p-4 space-y-4">
        <div>
          <h1 className="text-2xl font-bold mb-2">搜索和筛选组件测试</h1>
          <p className="text-muted-foreground">
            这是一个搜索和筛选组件的演示页面，包含搜索框、日期范围选择器和状态筛选器。
          </p>
        </div>

        {/* 筛选工具栏 */}
        <Card className="p-4">
          <FilterToolbar
            filters={filters}
            onFiltersChange={setFilters}
            onRefresh={handleRefresh}
            isLoading={isLoading}
            config={{
              showSearch: true,
              showDateRange: true,
              showStatusFilter: true,
              showClearAll: true,
              statusFilterOptions: statusOptions,
              statusFilterMultiple: true,
              statusFilterPlaceholder: '选择状态',
              searchPlaceholder: '搜索请求ID、接口、方法或IP...',
              dateRangePlaceholder: '选择日期范围'
            }}
          />
        </Card>

        {/* 筛选结果统计 */}
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <span>
            显示 {filteredData.length} 条记录，共 {originalData.length} 条
          </span>
          {filteredData.length !== originalData.length && (
            <span>已筛选 {originalData.length - filteredData.length} 条</span>
          )}
        </div>

        {/* 数据表格 */}
        <ResponsiveDataTable<LogData>
          columns={columns}
          data={filteredData}
          loading={{ isLoading }}
          config={{
            enableSorting: true,
            enableFiltering: false, // 禁用表格内置搜索，使用外部筛选
            enablePagination: true,
            pageSize: 10,
            pageSizeOptions: [5, 10, 20]
          }}
          emptyMessage="没有找到匹配的请求记录"
        />
      </div>
    </AppLayout>
  )
}