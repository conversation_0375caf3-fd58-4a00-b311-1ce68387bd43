{"title": "通知渠道", "createChannel": "创建渠道", "editChannel": "编辑渠道", "deleteChannel": "删除渠道", "channelName": "渠道名称", "channelType": "渠道类型", "channelDescription": "渠道描述", "wechatBot": "微信机器人", "feishuBot": "飞书机器人", "customWebhook": "自定义Webhook", "webhookUrl": "Webhook地址", "httpMethod": "HTTP方法", "requestHeaders": "请求头", "requestBody": "请求体", "urlParameters": "URL参数", "builtinTemplate": "内置模板", "customTemplate": "自定义模板", "templateVariables": "模板变量", "messageTemplate": "消息模板", "testChannel": "测试渠道", "testMessage": "测试消息", "sendTest": "发送测试", "testHistory": "测试历史", "channelStatus": "渠道状态", "lastTestTime": "最后测试时间", "testSuccess": "测试成功", "testFailed": "测试失败", "variableHelp": "变量帮助", "supportedVariables": "支持的变量", "conditionalLogic": "条件逻辑", "ifElseStatement": "if/else语句", "arithmeticExpression": "算术表达式"}