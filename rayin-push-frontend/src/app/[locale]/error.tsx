'use client'

import { useEffect } from 'react'
import { useParams } from 'next/navigation'
import { DefaultErrorFallback } from '@/components/error-boundary'

export default function LocaleError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  const params = useParams()
  const locale = (params?.locale as string) || 'zh'

  useEffect(() => {
    // 记录错误到日志服务
    console.error('Locale error:', error, { locale })
  }, [error, locale])

  return <DefaultErrorFallback error={error} reset={reset} />
}