'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { useLocale } from '@/hooks/use-locale'
import { preloadTranslations } from '@/hooks/use-enhanced-translation'
import type { SupportedLocale } from '@/types/i18n'

interface TranslationProviderState {
  isInitialized: boolean
  preloadedLocales: Set<SupportedLocale>
  isPreloading: boolean
  error: string | null
}

interface TranslationProviderProps {
  children: ReactNode
  fallbackLocale?: SupportedLocale
}

const TranslationContext = createContext<TranslationProviderState>({
  isInitialized: false,
  preloadedLocales: new Set(),
  isPreloading: false,
  error: null
})

export function TranslationProvider({ 
  children, 
  fallbackLocale = 'zh' 
}: TranslationProviderProps) {
  const { locale } = useLocale()
  const [state, setState] = useState<TranslationProviderState>({
    isInitialized: false,
    preloadedLocales: new Set(),
    isPreloading: false,
    error: null
  })

  // Preload translations for current locale
  useEffect(() => {
    let isCancelled = false

    const initializeTranslations = async () => {
      if (state.preloadedLocales.has(locale as SupportedLocale)) {
        setState(prev => ({ ...prev, isInitialized: true, isPreloading: false }))
        return
      }

      setState(prev => ({ ...prev, isPreloading: true, error: null }))

      try {
        await preloadTranslations(locale as SupportedLocale)
        
        if (!isCancelled) {
          setState(prev => ({
            ...prev,
            isInitialized: true,
            isPreloading: false,
            preloadedLocales: new Set([...prev.preloadedLocales, locale as SupportedLocale]),
            error: null
          }))
        }
      } catch (error) {
        if (!isCancelled) {
          console.error('Failed to preload translations:', error)
          setState(prev => ({
            ...prev,
            isPreloading: false,
            error: `Failed to load translations for ${locale}`
          }))

          // Try fallback locale
          if (locale !== fallbackLocale) {
            try {
              await preloadTranslations(fallbackLocale)
              setState(prev => ({
                ...prev,
                isInitialized: true,
                error: null,
                preloadedLocales: new Set([...prev.preloadedLocales, fallbackLocale])
              }))
            } catch (fallbackError) {
              console.error('Fallback translation loading also failed:', fallbackError)
            }
          }
        }
      }
    }

    initializeTranslations()

    return () => {
      isCancelled = true
    }
  }, [locale, fallbackLocale, state.preloadedLocales])

  return (
    <TranslationContext.Provider value={state}>
      {children}
    </TranslationContext.Provider>
  )
}

export function useTranslationProvider() {
  const context = useContext(TranslationContext)
  
  if (context === undefined) {
    throw new Error('useTranslationProvider must be used within a TranslationProvider')
  }
  
  return context
}

// Loading component for translation initialization
export function TranslationLoader({ children }: { children: ReactNode }) {
  const { isInitialized, isPreloading, error } = useTranslationProvider()

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-red-500 mb-2">Translation Error</div>
          <div className="text-sm text-gray-600">{error}</div>
        </div>
      </div>
    )
  }

  if (!isInitialized || isPreloading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <div className="text-sm text-gray-600">Loading translations...</div>
        </div>
      </div>
    )
  }

  return <>{children}</>
}