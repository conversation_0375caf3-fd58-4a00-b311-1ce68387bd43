// 统一导出所有mock数据和API
export * from './users'
export * from './configs'
export * from './channels'
export * from './logs'
export * from './limits'
export * from './dashboard'
export * from './api'

// 导出数据类型
export type * from '@/types/data'

// 数据验证和一致性检查
export const validateMockData = () => {
  const issues: string[] = []
  
  // 这里可以添加数据一致性检查
  // 例如：检查接口配置中引用的渠道是否存在
  // 检查日志中的接口ID是否有效等
  
  if (issues.length > 0) {
    console.warn('Mock data validation issues:', issues)
  }
  
  return issues.length === 0
}

// 初始化mock数据
export const initializeMockData = () => {
  console.log('Initializing mock data...')
  
  const isValid = validateMockData()
  if (!isValid) {
    console.error('Mock data validation failed')
  }
  
  console.log('Mock data initialized successfully')
  console.log('Available modules:')
  console.log('- Users: 5 users with different roles and statuses')
  console.log('- Configs: 5 interface configurations with various parsing rules')
  console.log('- Channels: 5 notification channels (WeChat, Feishu, Webhook)')
  console.log('- Logs: 100+ request logs with different statuses and results')
  console.log('- Limits: 6 rate limiting rules for different scenarios')
  console.log('- Dashboard: Real-time statistics and trend data')
  
  return true
}