'use client'

import { ReactNode, useEffect, useState } from 'react'
import { ErrorBoundary } from '@/components/error-boundary'
import { useStoreInitialization } from '@/hooks/use-store'
import { PageLoading } from '@/components/loading-spinner'

interface AppProvidersProps {
  children: ReactNode
}

// 应用初始化组件
function AppInitializer({ children }: { children: ReactNode }) {
  const { isHydrated } = useStoreInitialization()
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
  }, [])

  // 等待客户端挂载和应用初始化完成
  if (!isMounted || !isHydrated) {
    return <PageLoading message="应用初始化中..." />
  }

  return <>{children}</>
}

// 应用提供者组件
export function AppProviders({ children }: AppProvidersProps) {
  return (
    <ErrorBoundary>
      <AppInitializer>
        {children}
      </AppInitializer>
    </ErrorBoundary>
  )
}

// 页面包装器组件
export function PageWrapper({ children }: { children: ReactNode }) {
  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-background">
        {children}
      </div>
    </ErrorBoundary>
  )
}