'use client'

import React from 'react'
import { Card } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import type { DataTableColumn } from '@/types/dataTable'

interface MobileDataCardProps<T = Record<string, unknown>> {
  columns: DataTableColumn<T>[]
  row: T
  index: number
  className?: string
}

export function MobileDataCard<T extends Record<string, unknown>>({
  columns,
  row,
  index,
  className
}: MobileDataCardProps<T>) {
  // 渲染单元格内容
  const renderCellValue = (column: DataTableColumn<T>, row: T) => {
    if (column.cell) {
      return column.cell(row)
    }
    
    if (column.accessorKey) {
      const value = row[column.accessorKey]
      return value !== null && value !== undefined ? String(value) : '-'
    }
    
    return '-'
  }

  // 主要字段（第一个显示的字段）
  const primaryColumn = columns[0]
  const primaryValue = renderCellValue(primaryColumn, row)

  // 次要字段
  const secondaryColumns = columns.slice(1)

  return (
    <Card className={cn("p-4 space-y-3", className)}>
      {/* 主要信息 */}
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <h3 className="font-medium text-sm truncate">
            {primaryValue}
          </h3>
          {secondaryColumns.length > 0 && (
            <p className="text-xs text-muted-foreground mt-1">
              #{index + 1}
            </p>
          )}
        </div>
      </div>

      {/* 详细信息 */}
      {secondaryColumns.length > 0 && (
        <div className="space-y-2">
          {secondaryColumns.map((column) => {
            const value = renderCellValue(column, row)
            
            return (
              <div key={column.id} className="flex items-center justify-between text-xs">
                <span className="text-muted-foreground font-medium">
                  {typeof column.header === 'string' ? column.header : column.id}
                </span>
                <span className="text-foreground font-medium max-w-[60%] truncate text-right">
                  {value}
                </span>
              </div>
            )
          })}
        </div>
      )}
    </Card>
  )
}