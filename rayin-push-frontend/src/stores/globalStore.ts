'use client'

import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { GlobalState, SidebarState, Theme } from '@/types/store'
import type { SupportedLocale } from '@/types/i18n'

const initialSidebarState: SidebarState = {
  isOpen: true,
  isCollapsed: false,
  isMobile: false,
}

export const useGlobalStore = create<GlobalState>()(
  persist(
    (set, get) => ({
      // 主题设置
      theme: 'system' as Theme,
      setTheme: (theme: Theme) => {
        set({ theme })
        // 应用主题到document
        if (typeof window !== 'undefined') {
          const root = window.document.documentElement
          root.classList.remove('light', 'dark')
          
          if (theme === 'system') {
            const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
            root.classList.add(systemTheme)
          } else {
            root.classList.add(theme)
          }
        }
      },

      // 语言设置
      locale: 'zh' as SupportedLocale,
      setLocale: (locale: SupportedLocale) => {
        set({ locale })
      },

      // 侧边栏状态
      sidebar: initialSidebarState,
      setSidebarOpen: (isOpen: boolean) => {
        set((state) => ({
          sidebar: { ...state.sidebar, isOpen }
        }))
      },
      setSidebarCollapsed: (isCollapsed: boolean) => {
        set((state) => ({
          sidebar: { ...state.sidebar, isCollapsed }
        }))
      },
      setSidebarMobile: (isMobile: boolean) => {
        set((state) => ({
          sidebar: { ...state.sidebar, isMobile }
        }))
      },
      toggleSidebar: () => {
        const { sidebar } = get()
        set({
          sidebar: { ...sidebar, isOpen: !sidebar.isOpen }
        })
      },
      toggleSidebarCollapse: () => {
        const { sidebar } = get()
        set({
          sidebar: { ...sidebar, isCollapsed: !sidebar.isCollapsed }
        })
      },
    }),
    {
      name: 'global-store',
      partialize: (state) => ({
        theme: state.theme,
        locale: state.locale,
        sidebar: {
          isCollapsed: state.sidebar.isCollapsed,
          // 不持久化 isOpen 和 isMobile，这些应该在每次加载时重新计算
        },
      }),
    }
  )
)