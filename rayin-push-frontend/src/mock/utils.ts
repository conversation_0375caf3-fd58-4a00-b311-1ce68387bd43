// 数据生成工具函数

// 随机选择数组中的元素
export const randomChoice = <T>(array: T[]): T => {
  return array[Math.floor(Math.random() * array.length)]
}

// 生成随机时间戳（在指定范围内）
export const randomTimestamp = (daysBack: number = 30): string => {
  const now = Date.now()
  const randomTime = now - Math.random() * daysBack * 24 * 60 * 60 * 1000
  return new Date(randomTime).toISOString()
}

// 生成随机IP地址
export const randomIP = (): string => {
  const segments = [
    randomChoice(['192.168', '10.0', '172.16']),
    Math.floor(Math.random() * 256),
    Math.floor(Math.random() * 256)
  ]
  return segments.join('.')
}

// 生成随机User-Agent
export const randomUserAgent = (): string => {
  const agents = [
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15',
    'Mozilla/5.0 (Android 13; Mobile; rv:109.0) Gecko/111.0',
    'PostmanRuntime/7.32.0',
    'curl/7.68.0',
    'AlertManager/1.0',
    'LogAnalyzer/2.1'
  ]
  return randomChoice(agents)
}

// 生成随机中文姓名
export const randomChineseName = (): string => {
  const surnames = ['张', '李', '王', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', '胡', '朱', '高']
  const names = ['伟', '芳', '娜', '秀英', '敏', '静', '丽', '强', '磊', '军', '洋', '勇', '艳', '杰', '涛', '明', '超', '娟', '秀兰']
  
  return randomChoice(surnames) + randomChoice(names) + (Math.random() > 0.7 ? randomChoice(names) : '')
}

// 生成随机英文名
export const randomEnglishName = (): string => {
  const firstNames = ['John', 'Jane', 'Michael', 'Sarah', 'David', 'Lisa', 'Robert', 'Emily', 'James', 'Jennifer']
  const lastNames = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez']
  
  return `${randomChoice(firstNames)} ${randomChoice(lastNames)}`
}

// 生成随机邮箱
export const randomEmail = (name?: string): string => {
  const domains = ['example.com', 'company.com', 'test.org', 'demo.net', 'sample.io']
  const username = name ? name.toLowerCase().replace(/\s+/g, '.') : `user${Math.floor(Math.random() * 1000)}`
  return `${username}@${randomChoice(domains)}`
}

// 生成随机状态
export const randomStatus = <T extends string>(statuses: T[]): T => {
  return randomChoice(statuses)
}

// 生成随机持续时间（毫秒）
export const randomDuration = (min: number = 100, max: number = 5000): number => {
  return Math.floor(Math.random() * (max - min) + min)
}

// 生成随机成功率
export const randomSuccessRate = (): number => {
  return Math.round((0.7 + Math.random() * 0.25) * 100) / 100
}

// 生成真实的接口名称
export const randomInterfaceName = (): string => {
  const types = ['用户', '订单', '支付', '商品', '库存', '物流', '评价', '退款']
  const actions = ['注册', '登录', '创建', '更新', '删除', '查询', '状态变更', '异常']
  const suffixes = ['通知', '告警', '提醒', '确认', '回调']
  
  return `${randomChoice(types)}${randomChoice(actions)}${randomChoice(suffixes)}`
}

// 生成随机URL
export const randomUrl = (): string => {
  const domains = ['api.example.com', 'webhook.company.com', 'notify.service.io', 'alert.monitor.net']
  const paths = ['/webhook', '/notify', '/callback', '/alert', '/push', '/message']
  const resources = ['user', 'order', 'payment', 'system', 'service', 'data']
  
  return `https://${randomChoice(domains)}${randomChoice(paths)}/${randomChoice(resources)}`
}

// 生成随机错误消息
export const randomErrorMessage = (): string => {
  const errors = [
    'Connection timeout',
    'Invalid webhook key',
    'Rate limit exceeded',
    'Service unavailable',
    'Authentication failed',
    'Invalid request format',
    'Network error',
    'Server internal error',
    'Permission denied',
    'Resource not found'
  ]
  return randomChoice(errors)
}

// 生成随机操作类型
export const randomOperationType = (): string => {
  const operations = [
    '创建用户', '删除用户', '修改用户', '重置密码', '登录', '登出',
    '创建配置', '修改配置', '删除配置', '测试配置',
    '创建渠道', '修改渠道', '删除渠道', '测试渠道',
    '创建规则', '修改规则', '删除规则', '启用规则', '禁用规则'
  ]
  return randomChoice(operations)
}

// 批量生成数据的辅助函数
export const generateBatch = <T>(
  generator: () => T,
  count: number
): T[] => {
  return Array.from({ length: count }, generator)
}

// 数据一致性验证
export const validateDataConsistency = {
  // 验证引用完整性
  checkReferences: (_data: unknown) => {
    // 这里可以添加各种数据一致性检查
    return true
  },
  
  // 验证数据格式
  checkFormats: (_data: unknown) => {
    // 验证邮箱格式、URL格式等
    return true
  },
  
  // 验证业务逻辑
  checkBusinessRules: (_data: unknown) => {
    // 验证业务规则，如日期逻辑等
    return true
  }
}