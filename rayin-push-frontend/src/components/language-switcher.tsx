'use client'

import { useLocale } from '@/hooks/use-locale'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Languages, Check } from 'lucide-react'

interface LanguageSwitchProps {
  compact?: boolean
  className?: string
}

export function LanguageSwitch({ compact = false, className }: LanguageSwitchProps) {
  const { switchLocale, locale } = useLocale()
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className={`h-8 w-8 p-0 ${className}`}
        >
          <Languages className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem 
          onClick={() => switchLocale('zh')} 
          className="flex items-center justify-between"
        >
          <div className="flex items-center">
            🇨🇳 中文
          </div>
          {locale === 'zh' && <Check className="h-4 w-4" />}
        </DropdownMenuItem>
        <DropdownMenuItem 
          onClick={() => switchLocale('en')} 
          className="flex items-center justify-between"
        >
          <div className="flex items-center">
            🇺🇸 English
          </div>
          {locale === 'en' && <Check className="h-4 w-4" />}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}