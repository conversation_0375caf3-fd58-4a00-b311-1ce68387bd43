// 接口配置数据类型
export interface InterfaceConfig {
  id: string
  name: string
  token: string
  description: string
  method: 'GET' | 'POST'
  status: 'enabled' | 'disabled'
  channels: string[]
  parsingRules: {
    type: 'get' | 'post-form' | 'post-multipart' | 'post-json' | 'post-plain'
    variableMapping: Record<string, string>
    regexPatterns?: Record<string, string>
  }
  createdTime: string
  updatedTime: string
}

// 通知渠道数据类型
export interface NotificationChannel {
  id: string
  name: string
  type: 'wechat' | 'feishu' | 'webhook'
  description: string
  status: 'active' | 'inactive'
  config: {
    url?: string
    method?: 'GET' | 'POST'
    headers?: Record<string, string>
    template?: string
    variables?: Record<string, string>
  }
  createdTime: string
  updatedTime: string
  lastTestAt?: string
  testStatus?: 'success' | 'failed'
  creator: string
}

// 请求日志数据类型
export interface RequestLog {
  id: string
  interfaceId: string
  interfaceName: string
  requestTime: string
  responseTime: string
  duration: number
  status: 'success' | 'failed' | 'processing' | 'partial'
  originalData: Record<string, unknown>
  processedData: Record<string, unknown>
  channelResults: {
    channelId: string
    channelName: string
    status: 'success' | 'failed'
    response?: string
    error?: string
    sentAt: string
  }[]
  errorMessage?: string
  clientIp: string
  userAgent?: string
}

// 请求限制规则数据类型
export interface RateLimit {
  id: string
  name: string
  description: string
  type: 'global' | 'per-ip' | 'per-user'
  timeWindow: number // 秒
  requestLimit: number
  status: 'enabled' | 'disabled'
  ipRules?: {
    type: 'whitelist' | 'blacklist'
    ips: string[]
  }
  createdTime: string
  updatedTime: string
  triggeredCount: number
  lastTriggered?: string
  creator: string
}

// 用户数据类型
export interface UserData {
  id: string
  username: string
  email: string
  role: 'admin' | 'user'
  status: 'active' | 'inactive' | 'suspended'
  avatar?: string
  createdTime: string
  lastLoginAt?: string
  loginCount: number
  creator?: string
}

// 仪表盘统计数据类型
export interface DashboardStats {
  totalUsers: number
  totalRequests: number
  successRate: number
  todayRequests: number
  activeConfigs: number
  activeChannels: number
  recentRequests: RequestLog[]
  requestTrends: {
    date: string
    requests: number
    success: number
    failed: number
  }[]
}

// 用户操作日志类型
export interface UserOperationLog {
  id: string
  userId: string
  username: string
  operation: string
  target: string
  details: string
  ip: string
  userAgent?: string
  createdTime: string
  success: boolean
}