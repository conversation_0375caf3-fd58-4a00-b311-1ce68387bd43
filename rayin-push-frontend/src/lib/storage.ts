'use client'

import type { PersistConfig } from '@/types/store'

// 创建安全的localStorage包装器
export const createSafeStorage = () => {
  if (typeof window === 'undefined') {
    // 服务端渲染时返回mock storage
    return {
      getItem: () => null,
      setItem: () => {},
      removeItem: () => {},
    }
  }

  return {
    getItem: (name: string) => {
      try {
        return localStorage.getItem(name)
      } catch (error) {
        console.warn('Failed to get item from localStorage:', error)
        return null
      }
    },
    setItem: (name: string, value: string) => {
      try {
        localStorage.setItem(name, value)
      } catch (error) {
        console.warn('Failed to set item to localStorage:', error)
      }
    },
    removeItem: (name: string) => {
      try {
        localStorage.removeItem(name)
      } catch (error) {
        console.warn('Failed to remove item from localStorage:', error)
      }
    },
  }
}

// 创建通用的persist配置
export const createPersistConfig = (
  name: string, 
  options: {
    version?: number
    partialize?: (state: unknown) => unknown
    onRehydrateStorage?: () => (state?: unknown, error?: Error) => void
  } = {}
): PersistConfig => ({
  name,
  version: options.version || 1,
  storage: createSafeStorage(),
  partialize: options.partialize,
  onRehydrateStorage: options.onRehydrateStorage,
})

// 清理所有存储数据的工具函数
export const clearAllStorage = () => {
  if (typeof window !== 'undefined') {
    try {
      // 清理特定的store数据
      const keysToRemove = [
        'global-store',
        'auth-store',
      ]
      
      keysToRemove.forEach(key => {
        localStorage.removeItem(key)
      })
      
      console.log('All store data cleared')
    } catch (error) {
      console.warn('Failed to clear storage:', error)
    }
  }
}

// 获取存储大小的工具函数
export const getStorageSize = () => {
  if (typeof window === 'undefined') return 0
  
  try {
    let totalSize = 0
    for (const key in localStorage) {
      if (localStorage.hasOwnProperty(key)) {
        totalSize += localStorage[key].length + key.length
      }
    }
    return totalSize
  } catch (error) {
    console.warn('Failed to calculate storage size:', error)
    return 0
  }
}

// 检查存储是否可用
export const isStorageAvailable = () => {
  if (typeof window === 'undefined') return false
  
  try {
    const testKey = '__storage_test__'
    localStorage.setItem(testKey, 'test')
    localStorage.removeItem(testKey)
    return true
  } catch (error) {
    return false
  }
}