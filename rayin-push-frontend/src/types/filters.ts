import { ReactNode } from 'react'

// 搜索组件属性
export interface SearchBoxProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  disabled?: boolean
  className?: string
  debounceMs?: number
  onSearch?: (value: string) => void
  onClear?: () => void
  showClearButton?: boolean
}

// 筛选器选项
export interface FilterOption<T = unknown> {
  label: string
  value: T
  count?: number
  disabled?: boolean
  data?: Record<string, unknown> // 添加额外数据字段
}

// 状态筛选器属性
export interface StatusFilterProps<T = unknown> {
  options: FilterOption<T>[]
  value?: T | T[]
  onChange: (value: T | T[] | undefined) => void
  placeholder?: string
  multiple?: boolean
  disabled?: boolean
  className?: string
  showClearButton?: boolean
  showSelectedItems?: boolean
  renderOption?: (option: FilterOption<T>, isSelected: boolean) => ReactNode
}

// 日期范围
export interface DateRange {
  start: Date | null
  end: Date | null
}

// 日期范围选择器属性
export interface DateRangePickerProps {
  value: DateRange
  onChange: (range: DateRange) => void
  disabled?: boolean
  className?: string
  placeholder?: string
  presets?: {
    label: string
    range: DateRange
  }[]
  maxDate?: Date
  minDate?: Date
}

// 筛选状态
export interface FilterState {
  search: string
  dateRange: DateRange
  status: unknown
}