import type { UserData, UserOperationLog } from '@/types/data'

// 模拟用户数据
export const mockUsers: UserData[] = [
  {
    id: '1',
    username: 'admin',
    email: '<EMAIL>',
    role: 'admin',
    status: 'active',
    createdTime: '2024-01-01T08:00:00Z',
    lastLoginAt: '2024-12-25T10:30:00Z',
    loginCount: 156,
  },
  {
    id: '2',
    username: 'zhang_wei',
    email: '<EMAIL>',
    role: 'user',
    status: 'active',
    createdTime: '2024-02-15T09:15:00Z',
    lastLoginAt: '2024-12-24T16:45:00Z',
    loginCount: 89,
    creator: 'admin',
  },
  {
    id: '3',
    username: 'li_ming',
    email: '<EMAIL>',
    role: 'user',
    status: 'active',
    createdTime: '2024-03-10T14:20:00Z',
    lastLoginAt: '2024-12-23T11:20:00Z',
    loginCount: 67,
    creator: 'admin',
  },
  {
    id: '4',
    username: 'wang_fang',
    email: '<EMAIL>',
    role: 'user',
    status: 'inactive',
    createdTime: '2024-04-05T13:30:00Z',
    lastLoginAt: '2024-11-15T09:10:00Z',
    loginCount: 23,
    creator: 'admin',
  },
  {
    id: '5',
    username: 'test_user',
    email: '<EMAIL>',
    role: 'user',
    status: 'suspended',
    createdTime: '2024-05-20T16:45:00Z',
    lastLoginAt: '2024-08-30T14:25:00Z',
    loginCount: 12,
    creator: 'admin',
  },
]

// 模拟用户操作日志
export const mockUserOperationLogs: UserOperationLog[] = [
  {
    id: '1',
    userId: '1',
    username: 'admin',
    operation: '创建用户',
    target: 'zhang_wei',
    details: '创建了新用户 zhang_wei',
    ip: '*************',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
    createdTime: '2024-12-25T10:30:00Z',
    success: true,
  },
  {
    id: '2',
    userId: '2',
    username: 'zhang_wei',
    operation: '登录',
    target: '系统',
    details: '用户登录系统',
    ip: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
    createdTime: '2024-12-24T16:45:00Z',
    success: true,
  },
  {
    id: '3',
    userId: '1',
    username: 'admin',
    operation: '暂停用户',
    target: 'test_user',
    details: '暂停用户 test_user 的账户',
    ip: '*************',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
    createdTime: '2024-12-23T14:20:00Z',
    success: true,
  },
  {
    id: '4',
    userId: '3',
    username: 'li_ming',
    operation: '修改密码',
    target: '自己',
    details: '修改了登录密码',
    ip: '*************',
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X)',
    createdTime: '2024-12-22T09:15:00Z',
    success: true,
  },
  {
    id: '5',
    userId: '1',
    username: 'admin',
    operation: '删除用户',
    target: 'old_user',
    details: '删除了用户 old_user',
    ip: '*************',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
    createdTime: '2024-12-21T11:40:00Z',
    success: true,
  },
]