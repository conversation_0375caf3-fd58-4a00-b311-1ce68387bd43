#!/usr/bin/env node

/**
 * Translation Validation Script
 * Ensures consistency across all language files
 */

const fs = require('fs')
const path = require('path')

const LOCALES_DIR = path.join(__dirname, '../src/locales')
const SUPPORTED_LANGUAGES = ['zh', 'en']

function validateTranslations() {
  const issues = []
  const namespaces = new Set()
  
  // Collect all namespaces
  SUPPORTED_LANGUAGES.forEach(lang => {
    const langDir = path.join(LOCALES_DIR, lang)
    if (fs.existsSync(langDir)) {
      fs.readdirSync(langDir).forEach(file => {
        if (file.endsWith('.json')) {
          namespaces.add(file.replace('.json', ''))
        }
      })
    }
  })

  // Validate each namespace across languages
  namespaces.forEach(namespace => {
    const translations = {}
    const keys = new Set()
    
    // Load translations for each language
    SUPPORTED_LANGUAGES.forEach(lang => {
      const filePath = path.join(LOCALES_DIR, lang, `${namespace}.json`)
      if (fs.existsSync(filePath)) {
        try {
          translations[lang] = JSON.parse(fs.readFileSync(filePath, 'utf8'))
          Object.keys(translations[lang]).forEach(key => keys.add(key))
        } catch (error) {
          issues.push(`❌ Invalid JSON in ${lang}/${namespace}.json: ${error.message}`)
        }
      } else {
        issues.push(`❌ Missing file: ${lang}/${namespace}.json`)
      }
    })
    
    // Check for missing keys
    keys.forEach(key => {
      SUPPORTED_LANGUAGES.forEach(lang => {
        if (translations[lang] && !translations[lang][key]) {
          issues.push(`⚠️  Missing key "${key}" in ${lang}/${namespace}.json`)
        }
      })
    })
    
    // Check for empty values
    SUPPORTED_LANGUAGES.forEach(lang => {
      if (translations[lang]) {
        Object.entries(translations[lang]).forEach(([key, value]) => {
          if (!value || value.trim() === '') {
            issues.push(`⚠️  Empty value for "${key}" in ${lang}/${namespace}.json`)
          }
        })
      }
    })
  })

  return { issues, namespaces: Array.from(namespaces) }
}

function main() {
  console.log('🔍 Validating translations...\n')
  
  const { issues, namespaces } = validateTranslations()
  
  console.log(`📊 Found ${namespaces.length} namespaces: ${namespaces.join(', ')}`)
  console.log(`📋 Supported languages: ${SUPPORTED_LANGUAGES.join(', ')}\n`)
  
  if (issues.length === 0) {
    console.log('✅ All translations are valid!')
  } else {
    console.log(`❌ Found ${issues.length} issues:\n`)
    issues.forEach(issue => console.log(issue))
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}

module.exports = { validateTranslations }