'use client'

import React from 'react'
import { Menu, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useSidebar } from '@/hooks/use-store'
import { cn } from '@/lib/utils'

interface MobileNavToggleProps {
  className?: string
}

export function MobileNavToggle({ className }: MobileNavToggleProps) {
  const { isOpen, isMobile, toggleSidebar } = useSidebar()

  // 只在移动端显示
  if (!isMobile) {
    return null
  }

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={toggleSidebar}
      className={cn("lg:hidden", className)}
      aria-label={isOpen ? "关闭导航菜单" : "打开导航菜单"}
    >
      {isOpen ? (
        <X className="h-5 w-5" />
      ) : (
        <Menu className="h-5 w-5" />
      )}
    </Button>
  )
}