'use client'

import Link from 'next/link'
import { useParams } from 'next/navigation'
import { useTypedTranslation } from '@/hooks/use-typed-translation'
import { useStoreInitialization } from '@/hooks/use-store'
import { LanguageSwitch } from '@/components/language-switcher'
import { ThemeSwitch } from '@/components/theme-switcher'
import { MockDataTest } from '@/components/mock-data-test'
import { ErrorBoundary } from '@/components/error-boundary'
import { Button } from '@/components/ui/button'
import { ArrowRight, LayoutDashboard } from 'lucide-react'

export default function HomePage() {
  const { t, isLoading } = useTypedTranslation('common')
  const { isHydrated } = useStoreInitialization()
  const params = useParams()
  const locale = params?.locale as string || 'zh'

  if (isLoading || !isHydrated) {
    return (
      <div className="flex flex-1 flex-col">
        <main className="flex-1">
          <div className="container mx-auto py-6">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        </main>
      </div>
    )
  }

  return (
    <ErrorBoundary>
      <div className="flex flex-1 flex-col">
        <header className="border-b">
          <div className="container mx-auto py-4 flex justify-between items-center">
            <h1 className="text-2xl font-bold">{t('welcome')}</h1>
            <div className="flex items-center gap-3">
              <ThemeSwitch />
              <LanguageSwitch />
            </div>
          </div>
        </header>
        <main className="flex-1">
          <div className="container mx-auto py-6 space-y-8">
            <div>
              <p className="text-muted-foreground text-lg mb-6">
                {t('description')}
              </p>

              {/* 快速访问 */}
              <div className="mb-8 p-6 bg-accent/50 rounded-lg">
                <h2 className="text-lg font-semibold mb-4">快速访问</h2>
                <div className="flex gap-4">
                  <Button asChild>
                    <Link href={`/${locale}/dashboard`}>
                      <LayoutDashboard className="h-4 w-4 mr-2" />
                      进入仪表盘
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Link>
                  </Button>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <Link 
                  href={`/${locale}/dashboard`}
                  className="p-4 border rounded-lg hover:shadow-md transition-shadow block"
                >
                  <h3 className="font-semibold mb-2">{t('dashboard')}</h3>
                  <p className="text-sm text-muted-foreground">查看系统概览和统计数据</p>
                </Link>
                <div className="p-4 border rounded-lg hover:shadow-md transition-shadow">
                  <h3 className="font-semibold mb-2">{t('config')}</h3>
                  <p className="text-sm text-muted-foreground">管理接口配置和规则</p>
                </div>
                <div className="p-4 border rounded-lg hover:shadow-md transition-shadow">
                  <h3 className="font-semibold mb-2">{t('channels')}</h3>
                  <p className="text-sm text-muted-foreground">配置通知渠道</p>
                </div>
                <div className="p-4 border rounded-lg hover:shadow-md transition-shadow">
                  <h3 className="font-semibold mb-2">{t('logs')}</h3>
                  <p className="text-sm text-muted-foreground">查看请求日志</p>
                </div>
                <div className="p-4 border rounded-lg hover:shadow-md transition-shadow">
                  <h3 className="font-semibold mb-2">{t('limits')}</h3>
                  <p className="text-sm text-muted-foreground">管理请求限制规则</p>
                </div>
                <div className="p-4 border rounded-lg hover:shadow-md transition-shadow">
                  <h3 className="font-semibold mb-2">{t('users')}</h3>
                  <p className="text-sm text-muted-foreground">用户管理</p>
                </div>
              </div>
            </div>

            <MockDataTest />
          </div>
        </main>
      </div>
    </ErrorBoundary>
  )
}