import { ReactNode } from 'react'

// 排序方向
export type SortDirection = 'asc' | 'desc' | null

// 列定义
export interface DataTableColumn<T = Record<string, unknown>> {
  id: string
  header: string | ReactNode
  accessorKey?: keyof T
  cell?: (row: T) => ReactNode
  sortable?: boolean
  filterable?: boolean
  width?: string | number
  minWidth?: string | number
  maxWidth?: string | number
  align?: 'left' | 'center' | 'right'
  className?: string
  headerClassName?: string
}

// 排序状态
export interface SortingState {
  id: string
  desc: boolean
}

// 筛选状态
export interface FilteringState {
  id: string
  value: unknown
}

// 分页状态
export interface PaginationState {
  pageIndex: number
  pageSize: number
}

// 表格数据
export interface DataTableData<T = Record<string, unknown>> {
  data: T[]
  pageCount?: number
  total?: number
}

// 加载状态
export interface LoadingState {
  isLoading: boolean
  error?: string | null
}

// 表格配置
export interface DataTableConfig {
  enableSorting?: boolean
  enableFiltering?: boolean
  enablePagination?: boolean
  enableRowSelection?: boolean
  enableColumnResizing?: boolean
  showToolbar?: boolean
  showFooter?: boolean
  pageSize?: number
  pageSizeOptions?: number[]
}

// 表格操作
export interface DataTableActions<T = Record<string, unknown>> {
  onSortingChange?: (sorting: SortingState[]) => void
  onFilteringChange?: (filtering: FilteringState[]) => void
  onPaginationChange?: (pagination: PaginationState) => void
  onRowSelectionChange?: (selectedRows: T[]) => void
  onRefresh?: () => void
}

// 表格属性
export interface DataTableProps<T = Record<string, unknown>> {
  columns: DataTableColumn<T>[]
  data: T[]
  loading?: LoadingState
  config?: DataTableConfig
  actions?: DataTableActions<T>
  className?: string
  emptyMessage?: string
  pageCount?: number
  total?: number
}