'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Search, Plus } from 'lucide-react'
import { useTypedTranslation } from '@/hooks/use-typed-translation'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

interface ConfigToolbarProps {
  searchQuery: string
  onSearchChange: (query: string) => void
  statusFilter: 'all' | 'enabled' | 'disabled'
  onStatusFilterChange: (status: 'all' | 'enabled' | 'disabled') => void
  onCreateConfig: () => void
}

export function ConfigToolbar({
  searchQuery,
  onSearchChange,
  statusFilter,
  onStatusFilterChange,
  onCreateConfig
}: ConfigToolbarProps) {
  const { t } = useTypedTranslation('config')

  return (
    <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
      {/* 左侧搜索和筛选 */}
      <div className="flex flex-col sm:flex-row gap-3 flex-1 max-w-md">
        {/* 搜索框 */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t('searchPlaceholder')}
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* 状态筛选 */}
        <Select value={statusFilter} onValueChange={onStatusFilterChange}>
          <SelectTrigger className="w-[140px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t('allStatus')}</SelectItem>
            <SelectItem value="enabled">{t('enabled')}</SelectItem>
            <SelectItem value="disabled">{t('disabled')}</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* 右侧操作按钮 */}
      <div className="flex gap-2 items-center">
        {/* 创建配置按钮 */}
        <Button onClick={onCreateConfig} className="cursor-pointer">
          <Plus className="h-4 w-4 mr-1" />
          {t('createConfig')}
        </Button>
      </div>
    </div>
  )
}