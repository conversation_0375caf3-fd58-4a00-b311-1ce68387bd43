'use client'

import { useEffect, useState } from 'react'
import { usePathname } from 'next/navigation'

export function LoadingBar() {
  const [loading, setLoading] = useState(false)
  const [progress, setProgress] = useState(0)
  const pathname = usePathname()

  // 监听路由变化
  useEffect(() => {
    setLoading(false)
    setProgress(0)
  }, [pathname])

  // 监听导航链接点击
  useEffect(() => {
    const handleLinkClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement
      const link = target.closest('a[href]') as HTMLAnchorElement
      
      if (!link) return
      
      // 检查是否是内部链接
      const href = link.getAttribute('href')
      if (!href || href.startsWith('mailto:') || href.startsWith('tel:') || href.startsWith('http')) {
        return
      }

      // 检查是否是当前页面
      const currentPath = window.location.pathname
      if (href === currentPath) return

      // 开始加载动画
      setLoading(true)
      setProgress(5)

      // 模拟进度条动画
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 85) return prev + Math.random() * 2
          return prev + Math.random() * 12
        })
      }, 120)

      // 清理函数
      const cleanup = () => {
        clearInterval(progressInterval)
        setProgress(100)
        setTimeout(() => {
          setLoading(false)
          setProgress(0)
        }, 200)
      }

      // 设置最大加载时间
      const maxLoadingTime = setTimeout(cleanup, 3000)

      // 监听路由变化完成
      const checkRouteChange = () => {
        if (window.location.pathname !== currentPath) {
          cleanup()
          clearTimeout(maxLoadingTime)
        } else {
          setTimeout(checkRouteChange, 100)
        }
      }
      
      setTimeout(checkRouteChange, 100)
    }

    document.addEventListener('click', handleLinkClick)
    return () => document.removeEventListener('click', handleLinkClick)
  }, [])

  if (!loading) return null

  return (
    <div className="fixed top-0 left-0 right-0 z-50 w-full">
      <div className="h-0.5 bg-gray-200/30 w-full">
        <div
          className="h-full bg-gradient-to-r from-blue-400/60 via-purple-400/60 to-indigo-400/60 transition-all duration-200 ease-out"
          style={{
            width: `${progress}%`,
            boxShadow: '0 0 4px rgba(59, 130, 246, 0.3)',
          }}
        />
      </div>
    </div>
  )
}