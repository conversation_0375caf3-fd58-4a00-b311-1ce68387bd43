'use client'

import React, { useState, useMemo } from 'react'
import {
  ChevronUp,
  ChevronDown,
  ChevronsUpDown,
  RotateCcw,
  Loader2
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { cn } from '@/lib/utils'
import { useTypedTranslation } from '@/hooks/use-typed-translation'
import type {
  DataTableProps,
  DataTableColumn,
  SortingState,
  FilteringState,
  PaginationState
} from '@/types/dataTable'

export function DataTable<T extends Record<string, unknown>>({
  columns,
  data,
  loading = { isLoading: false },
  config = {},
  actions = {},
  className,
  emptyMessage,
  total
}: DataTableProps<T>) {
  const { t } = useTypedTranslation('common')
  
  // 默认配置
  const defaultConfig = {
    enableSorting: true,
    enableFiltering: true,
    enablePagination: true,
    enableRowSelection: false,
    showToolbar: true,
    showFooter: true,
    pageSize: 10,
    pageSizeOptions: [10, 20, 50, 100],
    ...config
  }

  // 状态管理
  const [sorting, setSorting] = useState<SortingState[]>([])
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: defaultConfig.pageSize
  })
  const [globalFilter, setGlobalFilter] = useState('')

  // 处理排序
  const handleSortingChange = (columnId: string) => {
    if (!defaultConfig.enableSorting) return

    const existingSort = sorting.find(s => s.id === columnId)
    let newSorting: SortingState[]

    if (!existingSort) {
      newSorting = [{ id: columnId, desc: false }]
    } else if (!existingSort.desc) {
      newSorting = [{ id: columnId, desc: true }]
    } else {
      newSorting = []
    }

    setSorting(newSorting)
    actions.onSortingChange?.(newSorting)
  }

  // 获取排序图标
  const getSortIcon = (columnId: string) => {
    const sort = sorting.find(s => s.id === columnId)
    if (!sort) return <ChevronsUpDown className="h-4 w-4" />
    return sort.desc ? 
      <ChevronDown className="h-4 w-4" /> : 
      <ChevronUp className="h-4 w-4" />
  }

  // 处理全局搜索
  const filteredData = useMemo(() => {
    if (!globalFilter) return data
    
    return data.filter((row) =>
      Object.values(row).some((value) =>
        String(value).toLowerCase().includes(globalFilter.toLowerCase())
      )
    )
  }, [data, globalFilter])

  // 处理分页
  const paginatedData = useMemo(() => {
    if (!defaultConfig.enablePagination) return filteredData
    
    const startIndex = pagination.pageIndex * pagination.pageSize
    const endIndex = startIndex + pagination.pageSize
    return filteredData.slice(startIndex, endIndex)
  }, [filteredData, pagination, defaultConfig.enablePagination])

  // 分页信息
  const totalPages = Math.ceil(filteredData.length / pagination.pageSize)
  const displayedTotal = total || filteredData.length

  // 渲染单元格内容
  const renderCell = (column: DataTableColumn<T>, row: T) => {
    if (column.cell) {
      return column.cell(row)
    }
    
    if (column.accessorKey) {
      const value = row[column.accessorKey]
      return value !== null && value !== undefined ? String(value) : '-'
    }
    
    return '-'
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* 工具栏 */}
      {defaultConfig.showToolbar && (
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {/* 全局搜索 */}
            {defaultConfig.enableFiltering && (
              <Input
                placeholder={`${t('search')}...`}
                value={globalFilter}
                onChange={(e) => setGlobalFilter(e.target.value)}
                className="w-64"
              />
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {/* 刷新按钮 */}
            <Button
              variant="outline"
              size="sm"
              onClick={actions.onRefresh}
              disabled={loading.isLoading}
            >
              {loading.isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <RotateCcw className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      )}

      {/* 表格容器 */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              {columns.map((column) => (
                <TableHead
                  key={column.id}
                  className={cn(
                    "text-left",
                    column.align === 'center' && "text-center",
                    column.align === 'right' && "text-right",
                    column.sortable && defaultConfig.enableSorting && "cursor-pointer select-none hover:bg-muted/50",
                    column.headerClassName
                  )}
                  style={{
                    width: column.width,
                    minWidth: column.minWidth,
                    maxWidth: column.maxWidth
                  }}
                  onClick={() => column.sortable && handleSortingChange(column.id)}
                >
                  <div className="flex items-center space-x-2">
                    <span>{column.header}</span>
                    {column.sortable && defaultConfig.enableSorting && (
                      <span className="text-muted-foreground">
                        {getSortIcon(column.id)}
                      </span>
                    )}
                  </div>
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          
          <TableBody>
            {loading.isLoading ? (
              <TableRow>
                <TableCell 
                  colSpan={columns.length} 
                  className="h-24 text-center"
                >
                  <div className="flex items-center justify-center space-x-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>{t('loading')}</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : paginatedData.length === 0 ? (
              <TableRow>
                <TableCell 
                  colSpan={columns.length} 
                  className="h-24 text-center text-muted-foreground"
                >
                  {emptyMessage || '暂无数据'}
                </TableCell>
              </TableRow>
            ) : (
              paginatedData.map((row, index) => (
                <TableRow key={index} className="hover:bg-muted/50">
                  {columns.map((column) => (
                    <TableCell
                      key={column.id}
                      className={cn(
                        column.align === 'center' && "text-center",
                        column.align === 'right' && "text-right",
                        column.className
                      )}
                    >
                      {renderCell(column, row)}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* 错误状态 */}
      {loading.error && (
        <div className="text-center py-4 text-destructive">
          <p>{loading.error}</p>
        </div>
      )}

      {/* 分页器 */}
      {defaultConfig.enablePagination && defaultConfig.showFooter && paginatedData.length > 0 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            显示 {pagination.pageIndex * pagination.pageSize + 1} 到{' '}
            {Math.min((pagination.pageIndex + 1) * pagination.pageSize, displayedTotal)} 条，
            共 {displayedTotal} 条
          </div>
          
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <p className="text-sm font-medium">每页行数</p>
              <select
                value={pagination.pageSize}
                onChange={(e) => {
                  const newPageSize = Number(e.target.value)
                  const newPagination = {
                    pageIndex: 0,
                    pageSize: newPageSize
                  }
                  setPagination(newPagination)
                  actions.onPaginationChange?.(newPagination)
                }}
                className="h-8 w-[70px] rounded border border-input bg-background px-3 py-1 text-sm dark:border-gray-600 dark:bg-gray-800"
              >
                {defaultConfig.pageSizeOptions.map((size) => (
                  <option key={size} value={size}>
                    {size}
                  </option>
                ))}
              </select>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const newPagination = { ...pagination, pageIndex: 0 }
                  setPagination(newPagination)
                  actions.onPaginationChange?.(newPagination)
                }}
                disabled={pagination.pageIndex === 0}
              >
                首页
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const newPagination = { 
                    ...pagination, 
                    pageIndex: pagination.pageIndex - 1 
                  }
                  setPagination(newPagination)
                  actions.onPaginationChange?.(newPagination)
                }}
                disabled={pagination.pageIndex === 0}
              >
                上一页
              </Button>
              
              <div className="text-sm font-medium">
                第 {pagination.pageIndex + 1} 页，共 {totalPages} 页
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const newPagination = { 
                    ...pagination, 
                    pageIndex: pagination.pageIndex + 1 
                  }
                  setPagination(newPagination)
                  actions.onPaginationChange?.(newPagination)
                }}
                disabled={pagination.pageIndex >= totalPages - 1}
              >
                下一页
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const newPagination = { 
                    ...pagination, 
                    pageIndex: totalPages - 1 
                  }
                  setPagination(newPagination)
                  actions.onPaginationChange?.(newPagination)
                }}
                disabled={pagination.pageIndex >= totalPages - 1}
              >
                尾页
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}