'use client'

import { AppLayout } from '@/components/app-layout'
import { useTypedTranslation } from '@/hooks/use-typed-translation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Clock } from 'lucide-react'
import { useRouter, useParams } from 'next/navigation'
import { useStoreInitialization } from '@/hooks/use-store'

export default function LogDetailPage() {
  const { isLoading } = useTypedTranslation('common')
  const { isHydrated } = useStoreInitialization()
  const router = useRouter()
  const params = useParams()
  const logId = params?.id as string

  if (isLoading || !isHydrated) {
    return (
      <AppLayout title="日志详情">
        <div className="flex items-center justify-center h-full">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </AppLayout>
    )
  }

  const handleBack = () => {
    router.back()
  }

  return (
    <AppLayout title="日志详情">
      <div className="p-4 space-y-4">
        {/* 返回按钮 */}
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回
          </Button>
          <h1 className="text-2xl font-bold">日志详情</h1>
          <Badge variant="outline">ID: {logId}</Badge>
        </div>

        {/* 占位内容 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Clock className="h-5 w-5" />
              <span>请求日志详情</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center py-12 text-muted-foreground">
              <div className="mb-4">
                <Clock className="h-16 w-16 mx-auto opacity-50" />
              </div>
              <h3 className="text-lg font-semibold mb-2">日志详情页面开发中</h3>
              <p className="mb-4">
                这个页面将在任务 12.3 中实现，用于显示完整的请求日志详情。
              </p>
              <p className="text-sm">
                请求ID: <code className="bg-muted px-2 py-1 rounded">{logId}</code>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  )
}