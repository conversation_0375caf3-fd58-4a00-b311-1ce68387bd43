'use client'

import { create } from 'zustand'
import type { AppState } from '@/types/store'

export const useAppStore = create<AppState>((set) => ({
  isInitialized: false,
  isLoading: false,
  error: null,

  setInitialized: (initialized: boolean) => {
    set({ isInitialized: initialized })
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading })
  },

  setError: (error: string | null) => {
    set({ error })
  },

  clearError: () => {
    set({ error: null })
  },
}))