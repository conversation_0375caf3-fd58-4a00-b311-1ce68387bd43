'use client'

import { useParams } from 'next/navigation'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Home, ArrowLeft } from 'lucide-react'
import Link from 'next/link'

export default function LocaleNotFound() {
  const params = useParams()
  const locale = (params?.locale as string) || 'zh'
  
  const content = {
    zh: {
      title: '页面未找到',
      description: '抱歉，您访问的页面不存在或已被移动。',
      backHome: '回到首页',
      goBack: '返回上一页'
    },
    en: {
      title: 'Page Not Found',
      description: 'Sorry, the page you are looking for does not exist or has been moved.',
      backHome: 'Back to Home',
      goBack: 'Go Back'
    }
  }
  
  const text = content[locale as keyof typeof content] || content.zh

  const handleGoBack = () => {
    if (typeof window !== 'undefined') {
      window.history.back()
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-md p-8 text-center">
        <div className="text-6xl font-bold text-muted-foreground mb-4">404</div>
        <h2 className="text-2xl font-semibold mb-2">{text.title}</h2>
        <p className="text-muted-foreground mb-6">
          {text.description}
        </p>
        
        <div className="flex gap-3 justify-center">
          <Button asChild>
            <Link href={`/${locale}`}>
              <Home className="h-4 w-4 mr-2" />
              {text.backHome}
            </Link>
          </Button>
          <Button variant="outline" onClick={handleGoBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            {text.goBack}
          </Button>
        </div>
      </Card>
    </div>
  )
}