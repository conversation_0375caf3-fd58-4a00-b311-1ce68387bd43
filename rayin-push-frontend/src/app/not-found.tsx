'use client'

import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Home, ArrowLeft } from 'lucide-react'
import Link from 'next/link'

export default function NotFound() {
  const handleGoBack = () => {
    if (typeof window !== 'undefined') {
      window.history.back()
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-md p-8 text-center">
        <div className="text-6xl font-bold text-muted-foreground mb-4">404</div>
        <h2 className="text-2xl font-semibold mb-2">页面未找到</h2>
        <p className="text-muted-foreground mb-6">
          抱歉，您访问的页面不存在或已被移动。
        </p>
        
        <div className="flex gap-3 justify-center">
          <Button asChild>
            <Link href="/zh">
              <Home className="h-4 w-4 mr-2" />
              回到首页
            </Link>
          </Button>
          <Button variant="outline" onClick={handleGoBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回上一页
          </Button>
        </div>
      </Card>
    </div>
  )
}