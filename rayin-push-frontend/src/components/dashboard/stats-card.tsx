'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { ReactNode } from 'react'

interface StatisticsCardProps {
  title: string
  value: string | number
  icon?: ReactNode
  trend?: {
    value: number
    isPositive: boolean
    timeframe: string
  }
  loading?: boolean
  error?: string | null
  className?: string
}

export function StatisticsCard({
  title,
  value,
  icon,
  trend,
  loading,
  error,
  className
}: StatisticsCardProps) {
  if (loading) {
    return (
      <Card className={cn('relative overflow-hidden', className)}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            <div className="h-4 bg-muted rounded animate-pulse w-24"></div>
          </CardTitle>
          {icon && (
            <div className="h-4 w-4 bg-muted rounded animate-pulse"></div>
          )}
        </CardHeader>
        <CardContent>
          <div className="h-8 bg-muted rounded animate-pulse w-16 mb-2"></div>
          <div className="h-3 bg-muted rounded animate-pulse w-20"></div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={cn('relative overflow-hidden border-red-200', className)}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-red-600">
            {title}
          </CardTitle>
          {icon && (
            <div className="text-red-400">
              {icon}
            </div>
          )}
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-600">--</div>
          <p className="text-xs text-red-500 mt-1">
            {error}
          </p>
        </CardContent>
      </Card>
    )
  }

  const formatValue = (val: string | number) => {
    if (typeof val === 'number') {
      if (val >= 1000000) {
        return (val / 1000000).toFixed(1) + 'M'
      } else if (val >= 1000) {
        return (val / 1000).toFixed(1) + 'K'
      }
      return val.toLocaleString()
    }
    return val
  }

  const getTrendColor = () => {
    if (!trend) return ''
    return trend.isPositive ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
  }

  const getTrendBadgeVariant = () => {
    if (!trend) return 'secondary'
    return trend.isPositive ? 'default' : 'destructive'
  }

  return (
    <Card className={cn('relative overflow-hidden hover:shadow-md transition-all duration-200', className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        {icon && (
          <div className="h-4 w-4 text-muted-foreground">
            {icon}
          </div>
        )}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-foreground">
          {formatValue(value)}
        </div>
        {trend && (
          <div className="flex items-center space-x-2 mt-2">
            <Badge variant={getTrendBadgeVariant()} className="text-xs">
              {trend.isPositive ? '+' : ''}
              {trend.value > 0 ? trend.value.toFixed(1) : trend.value}%
            </Badge>
            <p className={cn('text-xs', getTrendColor())}>
              {trend.timeframe}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}