'use client'

import React from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { ChevronRight, Home } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useTypedTranslation } from '@/hooks/use-typed-translation'
import type { BreadcrumbItem, BreadcrumbProps } from '@/types/breadcrumb'

export function Breadcrumb({ 
  items, 
  separator = <ChevronRight className="h-4 w-4" />,
  className 
}: BreadcrumbProps) {
  const { t } = useTypedTranslation('common')

  return (
    <nav className={cn("flex items-center space-x-1 text-sm", className)}>
      {items.map((item, index) => {
        const isLast = index === items.length - 1
        const title = item.titleKey ? t(item.titleKey as keyof typeof t) : item.title
        
        return (
          <React.Fragment key={index}>
            <div className="flex items-center">
              {item.icon && (
                <item.icon className="h-4 w-4 mr-1" />
              )}
              
              {item.href && !isLast ? (
                <Link 
                  href={item.href}
                  className="text-muted-foreground hover:text-foreground transition-colors"
                >
                  {title}
                </Link>
              ) : (
                <span className={cn(
                  isLast ? "text-foreground font-medium" : "text-muted-foreground"
                )}>
                  {title}
                </span>
              )}
            </div>
            
            {!isLast && (
              <div className="text-muted-foreground/50">
                {separator}
              </div>
            )}
          </React.Fragment>
        )
      })}
    </nav>
  )
}

// 根据路径自动生成面包屑的 Hook
export function useBreadcrumb() {
  const pathname = usePathname()
  const { t } = useTypedTranslation('common')
  
  const generateBreadcrumb = React.useCallback((customItems?: BreadcrumbItem[]): BreadcrumbItem[] => {
    if (customItems) {
      return [
        { title: t('dashboard'), titleKey: 'dashboard', href: '/dashboard', icon: Home },
        ...customItems
      ]
    }

    // 从路径自动生成
    const segments = pathname.split('/').filter(Boolean)
    const breadcrumbItems: BreadcrumbItem[] = [
      { title: t('dashboard'), titleKey: 'dashboard', href: '/dashboard', icon: Home }
    ]

    // 移除 locale 段
    const pathSegments = segments.slice(1) // 假设第一个是 locale

    let currentPath = `/${segments[0]}` // 保留 locale

    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`
      
      const isLast = index === pathSegments.length - 1
      
      // 根据路径段生成标题
      let title = segment
      let titleKey: string | undefined
      
      switch (segment) {
        case 'configs':
          title = t('config')
          titleKey = 'config'
          break
        case 'channels':
          title = t('channels')
          titleKey = 'channels'
          break
        case 'logs':
          title = t('logs')
          titleKey = 'logs'
          break
        case 'limits':
          title = t('limits')
          titleKey = 'limits'
          break
        case 'users':
          title = t('users')
          titleKey = 'users'
          break
        case 'monitoring':
          title = t('monitoring')
          titleKey = 'monitoring'
          break
        case 'analytics':
          title = t('analytics')
          titleKey = 'analytics'
          break
        case 'webhooks':
          title = t('webhooks')
          titleKey = 'webhooks'
          break
        case 'database':
          title = t('database')
          titleKey = 'database'
          break
        default:
          // 如果是数字ID，显示为详情页
          if (/^\d+$/.test(segment)) {
            title = '详情'
          } else {
            // 首字母大写
            title = segment.charAt(0).toUpperCase() + segment.slice(1)
          }
      }
      
      breadcrumbItems.push({
        title,
        titleKey,
        href: isLast ? undefined : currentPath
      })
    })

    return breadcrumbItems
  }, [pathname, t])

  return { generateBreadcrumb }
}