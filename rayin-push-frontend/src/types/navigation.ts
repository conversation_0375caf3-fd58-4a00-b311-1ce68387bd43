import { LucideIcon } from 'lucide-react'

export interface NavigationItem {
  id: string
  title: string
  titleKey: string // 翻译key
  icon: LucideIcon
  href: string
  badge?: string | number
  children?: NavigationItem[]
  requireAuth?: boolean
  roles?: ('admin' | 'user')[]
}

export interface NavigationSection {
  id: string
  title?: string
  titleKey?: string // 翻译key
  items: NavigationItem[]
}

export interface SidebarProps {
  isOpen: boolean
  isCollapsed: boolean
  isMobile: boolean
  onToggle: () => void
  onCollapse: () => void
  onClose: () => void
}